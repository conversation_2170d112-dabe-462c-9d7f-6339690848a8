<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Widget</class>
 <widget class="QWidget" name="Widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1271</width>
    <height>813</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>主界面</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <spacer name="horizontalSpacer_6">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>478</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="0" column="1">
    <widget class="QLabel" name="label_5">
     <property name="font">
      <font>
       <pointsize>14</pointsize>
      </font>
     </property>
     <property name="text">
      <string>时间：</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QLabel" name="label_time">
     <property name="font">
      <font>
       <pointsize>15</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(53, 132, 228);</string>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="0" column="3">
    <spacer name="horizontalSpacer_7">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>478</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="1" column="0" colspan="4">
    <layout class="QVBoxLayout" name="verticalLayout_5">
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>224</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>169</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <widget class="QPushButton" name="node1Bt">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>100</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border-image: url(:/node_pic.png);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label">
             <property name="font">
              <font>
               <pointsize>13</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Node1</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <widget class="QPushButton" name="node2Bt">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>100</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border-image: url(:/node2.png);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_2">
             <property name="font">
              <font>
               <pointsize>13</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Node2</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <widget class="QPushButton" name="node3Bt">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>100</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border-image: url(:/node3.png);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="font">
              <font>
               <pointsize>13</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Node3</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <widget class="QPushButton" name="setBt">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>100</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">border-image: url(:/set.png);</string>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_4">
             <property name="font">
              <font>
               <pointsize>13</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Set</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>168</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>223</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
