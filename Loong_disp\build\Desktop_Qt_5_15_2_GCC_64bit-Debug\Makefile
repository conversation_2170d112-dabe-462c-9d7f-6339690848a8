#############################################################################
# Makefile for building: Loong_disp
# Generated by qmake (3.1) (Qt 5.15.2)
# Project:  ../../Loong_disp.pro
# Template: app
# Command: /home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/qmake -o Makefile ../../Loong_disp.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DQT_QML_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SERIALPORT_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -g -Wall -Wextra -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -g -std=gnu++1z -Wall -Wextra -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I../../../Loong_disp -I. -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/libdrm -I. -I/usr/include/openssl -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++
QMAKE         = /home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/qmake -install qinstall
QINSTALL_PROGRAM = /home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = Loong_disp1.0.0
DISTDIR = /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/.tmp/Loong_disp1.0.0
LINK          = g++
LFLAGS        = -Wl,-rpath,/home/<USER>/Qt_5_15/5.15.2/gcc_64/lib
LIBS          = $(SUBLIBS) -L/usr/lib/x86_64-linux-gnu /home/<USER>/Qt_5_15/5.15.2/gcc_64/lib/libQt5Charts.so /home/<USER>/Qt_5_15/5.15.2/gcc_64/lib/libQt5Widgets.so /home/<USER>/Qt_5_15/5.15.2/gcc_64/lib/libQt5Gui.so /home/<USER>/Qt_5_15/5.15.2/gcc_64/lib/libQt5SerialPort.so /home/<USER>/Qt_5_15/5.15.2/gcc_64/lib/libQt5Network.so /home/<USER>/Qt_5_15/5.15.2/gcc_64/lib/libQt5Core.so -lGL -lpthread   
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ../../main.cpp \
		../../node1.cpp \
		../../node2.cpp \
		../../node3.cpp \
		../../setting.cpp \
		../../taskmqtt.cpp \
		../../taskserialport.cpp \
		../../taskwarning.cpp \
		../../widget.cpp \
		../../mqtt/qmqtt_client.cpp \
		../../mqtt/qmqtt_client_p.cpp \
		../../mqtt/qmqtt_frame.cpp \
		../../mqtt/qmqtt_message.cpp \
		../../mqtt/qmqtt_network.cpp \
		../../mqtt/qmqtt_router.cpp \
		../../mqtt/qmqtt_routesubscription.cpp \
		../../mqtt/qmqtt_socket.cpp \
		../../mqtt/qmqtt_ssl_socket.cpp \
		../../mqtt/qmqtt_timer.cpp \
		../../mqtt/qmqtt_websocket.cpp \
		../../mqtt/qmqtt_websocketiodevice.cpp qrc_pic.cpp \
		moc_node1.cpp \
		moc_node2.cpp \
		moc_node3.cpp \
		moc_setting.cpp \
		moc_taskmqtt.cpp \
		moc_taskserialport.cpp \
		moc_taskwarning.cpp \
		moc_widget.cpp \
		moc_qmqtt_client.cpp \
		moc_qmqtt_network_p.cpp \
		moc_qmqtt_networkinterface.cpp \
		moc_qmqtt_router.cpp \
		moc_qmqtt_routesubscription.cpp \
		moc_qmqtt_socket_p.cpp \
		moc_qmqtt_socketinterface.cpp \
		moc_qmqtt_ssl_socket_p.cpp \
		moc_qmqtt_timer_p.cpp \
		moc_qmqtt_timerinterface.cpp \
		moc_qmqtt_websocket_p.cpp \
		moc_qmqtt_websocketiodevice_p.cpp
OBJECTS       = main.o \
		node1.o \
		node2.o \
		node3.o \
		setting.o \
		taskmqtt.o \
		taskserialport.o \
		taskwarning.o \
		widget.o \
		qmqtt_client.o \
		qmqtt_client_p.o \
		qmqtt_frame.o \
		qmqtt_message.o \
		qmqtt_network.o \
		qmqtt_router.o \
		qmqtt_routesubscription.o \
		qmqtt_socket.o \
		qmqtt_ssl_socket.o \
		qmqtt_timer.o \
		qmqtt_websocket.o \
		qmqtt_websocketiodevice.o \
		qrc_pic.o \
		moc_node1.o \
		moc_node2.o \
		moc_node3.o \
		moc_setting.o \
		moc_taskmqtt.o \
		moc_taskserialport.o \
		moc_taskwarning.o \
		moc_widget.o \
		moc_qmqtt_client.o \
		moc_qmqtt_network_p.o \
		moc_qmqtt_networkinterface.o \
		moc_qmqtt_router.o \
		moc_qmqtt_routesubscription.o \
		moc_qmqtt_socket_p.o \
		moc_qmqtt_socketinterface.o \
		moc_qmqtt_ssl_socket_p.o \
		moc_qmqtt_timer_p.o \
		moc_qmqtt_timerinterface.o \
		moc_qmqtt_websocket_p.o \
		moc_qmqtt_websocketiodevice_p.o
DIST          = ../../mqtt/qmqtt.pri \
		../../mqtt/qmqtt.qbs \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/spec_pre.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/unix.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/linux.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/sanitize.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/gcc-base.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/g++-base.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/g++-unix.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/qconfig.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		/home/<USER>/qtmqtt-5.15.2/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/mkspecs/modules-inst/qt_lib_mqtt.pri \
		/home/<USER>/qtmqtt-5.15.2/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/mkspecs/modules-inst/qt_lib_mqtt_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_mqtt.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlmodels.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_script.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt_functions.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt_config.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++/qmake.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/spec_post.prf \
		../../.qmake.stash \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/exclusive_builds.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/toolchain.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/default_pre.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resolve_config.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/default_post.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qml_debug.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/warn_on.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resources_functions.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resources.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/moc.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/unix/opengl.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/uic.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/unix/thread.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qmake_use.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/file_copies.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/testcase_targets.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/exceptions.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/yacc.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/lex.prf \
		../../../../Loong_disp.pro ../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskmqtt.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_client_p.h \
		../../mqtt/qmqtt_frame.h \
		../../mqtt/qmqtt_global.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_message_p.h \
		../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_routedmessage.h \
		../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_routesubscription.h \
		../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_websocket_p.h \
		../../mqtt/qmqtt_websocketiodevice_p.h ../../main.cpp \
		../../node1.cpp \
		../../node2.cpp \
		../../node3.cpp \
		../../setting.cpp \
		../../taskmqtt.cpp \
		../../taskserialport.cpp \
		../../taskwarning.cpp \
		../../widget.cpp \
		../../mqtt/qmqtt_client.cpp \
		../../mqtt/qmqtt_client_p.cpp \
		../../mqtt/qmqtt_frame.cpp \
		../../mqtt/qmqtt_message.cpp \
		../../mqtt/qmqtt_network.cpp \
		../../mqtt/qmqtt_router.cpp \
		../../mqtt/qmqtt_routesubscription.cpp \
		../../mqtt/qmqtt_socket.cpp \
		../../mqtt/qmqtt_ssl_socket.cpp \
		../../mqtt/qmqtt_timer.cpp \
		../../mqtt/qmqtt_websocket.cpp \
		../../mqtt/qmqtt_websocketiodevice.cpp
QMAKE_TARGET  = Loong_disp
DESTDIR       = 
TARGET        = Loong_disp


first: all
####### Build rules

Loong_disp: ui_node1.h ui_node2.h ui_node3.h ui_setting.h ui_widget.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ../../Loong_disp.pro /home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++/qmake.conf /home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/spec_pre.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/unix.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/linux.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/sanitize.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/gcc-base.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/gcc-base-unix.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/g++-base.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/g++-unix.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/qconfig.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_core.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_help.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_location.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		../../../../mqtt/qtmqtt-5.15.2/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/mkspecs/modules-inst/qt_lib_mqtt.pri \
		../../../../mqtt/qtmqtt-5.15.2/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/mkspecs/modules-inst/qt_lib_mqtt_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_mqtt.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_network.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlmodels.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_script.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt_functions.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt_config.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++/qmake.conf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/exclusive_builds.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/toolchain.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/default_pre.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resolve_config.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/default_post.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qml_debug.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/warn_on.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resources_functions.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resources.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/moc.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/unix/opengl.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/uic.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/unix/thread.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qmake_use.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/file_copies.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/testcase_targets.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/exceptions.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/yacc.prf \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/lex.prf \
		../../Loong_disp.pro \
		../../pic/pic.qrc
	$(QMAKE) -o Makefile ../../Loong_disp.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/spec_pre.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/unix.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/linux.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/sanitize.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/gcc-base.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/gcc-base-unix.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/g++-base.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/common/g++-unix.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/qconfig.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3danimation.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dcore.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dextras.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dinput.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquick.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3drender.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_charts.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_charts_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_concurrent.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_core.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_core_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_dbus.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designer.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designer_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_edid_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gamepad.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gui.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_gui_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_help.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_help_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_location.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_location_private.pri:
../../../../mqtt/qtmqtt-5.15.2/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/mkspecs/modules-inst/qt_lib_mqtt.pri:
../../../../mqtt/qtmqtt-5.15.2/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/mkspecs/modules-inst/qt_lib_mqtt_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_mqtt.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimedia.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediagsttools_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_network.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_network_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_nfc.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_opengl.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioning.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioningquick.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_printsupport.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qml.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qml_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlmodels.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlmodels_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmltest.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlworkerscript.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quick.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quick_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_repparser.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_script.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_script_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scripttools.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scxml.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sensors.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialbus.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialport.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sql.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_sql_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_svg.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_svg_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_testlib.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uitools.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_waylandclient.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_waylandclient_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webchannel.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_websockets.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webview.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_webview_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_widgets.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_x11extras.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xkbcommon_support_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xml.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xml_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt_functions.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt_config.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++/qmake.conf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/spec_post.prf:
.qmake.stash:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/exclusive_builds.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/toolchain.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/default_pre.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resolve_config.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/default_post.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qml_debug.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/warn_on.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qt.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resources_functions.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/resources.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/moc.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/unix/opengl.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/uic.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/unix/thread.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/qmake_use.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/file_copies.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/testcase_targets.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/exceptions.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/yacc.prf:
/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/lex.prf:
../../Loong_disp.pro:
../../pic/pic.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile ../../Loong_disp.pro -spec linux-g++ CONFIG+=debug CONFIG+=qml_debug

qmake_all: FORCE


all: Makefile Loong_disp

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../../pic/pic.qrc $(DISTDIR)/
	$(COPY_FILE) --parents /home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../../node1.h ../../node2.h ../../node3.h ../../setting.h ../../taskmqtt.h ../../taskserialport.h ../../taskwarning.h ../../widget.h ../../mqtt/qmqtt.h ../../mqtt/qmqtt_client.h ../../mqtt/qmqtt_client_p.h ../../mqtt/qmqtt_frame.h ../../mqtt/qmqtt_global.h ../../mqtt/qmqtt_message.h ../../mqtt/qmqtt_message_p.h ../../mqtt/qmqtt_network_p.h ../../mqtt/qmqtt_networkinterface.h ../../mqtt/qmqtt_routedmessage.h ../../mqtt/qmqtt_router.h ../../mqtt/qmqtt_routesubscription.h ../../mqtt/qmqtt_socket_p.h ../../mqtt/qmqtt_socketinterface.h ../../mqtt/qmqtt_ssl_socket_p.h ../../mqtt/qmqtt_timer_p.h ../../mqtt/qmqtt_timerinterface.h ../../mqtt/qmqtt_websocket_p.h ../../mqtt/qmqtt_websocketiodevice_p.h $(DISTDIR)/
	$(COPY_FILE) --parents ../../main.cpp ../../node1.cpp ../../node2.cpp ../../node3.cpp ../../setting.cpp ../../taskmqtt.cpp ../../taskserialport.cpp ../../taskwarning.cpp ../../widget.cpp ../../mqtt/qmqtt_client.cpp ../../mqtt/qmqtt_client_p.cpp ../../mqtt/qmqtt_frame.cpp ../../mqtt/qmqtt_message.cpp ../../mqtt/qmqtt_network.cpp ../../mqtt/qmqtt_router.cpp ../../mqtt/qmqtt_routesubscription.cpp ../../mqtt/qmqtt_socket.cpp ../../mqtt/qmqtt_ssl_socket.cpp ../../mqtt/qmqtt_timer.cpp ../../mqtt/qmqtt_websocket.cpp ../../mqtt/qmqtt_websocketiodevice.cpp $(DISTDIR)/
	$(COPY_FILE) --parents ../../node1.ui ../../node2.ui ../../node3.ui ../../setting.ui ../../widget.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all: qrc_pic.cpp
compiler_rcc_clean:
	-$(DEL_FILE) qrc_pic.cpp
qrc_pic.cpp: ../../pic/pic.qrc \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/rcc \
		../../pic/node3.png \
		../../pic/smog1_img.png \
		../../pic/humi1_image.png \
		../../pic/node2.png \
		../../pic/temp1_img.png \
		../../pic/back.png \
		../../pic/node_pic.png \
		../../pic/pic.qrc \
		../../pic/light1_img.png \
		../../pic/set.png
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/rcc -name pic ../../pic/pic.qrc -o qrc_pic.cpp

compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/data/dummy.cpp
	g++ -pipe -g -std=gnu++1z -Wall -Wextra -dM -E -o moc_predefs.h /home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_node1.cpp moc_node2.cpp moc_node3.cpp moc_setting.cpp moc_taskmqtt.cpp moc_taskserialport.cpp moc_taskwarning.cpp moc_widget.cpp moc_qmqtt_client.cpp moc_qmqtt_network_p.cpp moc_qmqtt_networkinterface.cpp moc_qmqtt_router.cpp moc_qmqtt_routesubscription.cpp moc_qmqtt_socket_p.cpp moc_qmqtt_socketinterface.cpp moc_qmqtt_ssl_socket_p.cpp moc_qmqtt_timer_p.cpp moc_qmqtt_timerinterface.cpp moc_qmqtt_websocket_p.cpp moc_qmqtt_websocketiodevice_p.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_node1.cpp moc_node2.cpp moc_node3.cpp moc_setting.cpp moc_taskmqtt.cpp moc_taskserialport.cpp moc_taskwarning.cpp moc_widget.cpp moc_qmqtt_client.cpp moc_qmqtt_network_p.cpp moc_qmqtt_networkinterface.cpp moc_qmqtt_router.cpp moc_qmqtt_routesubscription.cpp moc_qmqtt_socket_p.cpp moc_qmqtt_socketinterface.cpp moc_qmqtt_ssl_socket_p.cpp moc_qmqtt_timer_p.cpp moc_qmqtt_timerinterface.cpp moc_qmqtt_websocket_p.cpp moc_qmqtt_websocketiodevice_p.cpp
moc_node1.cpp: ../../node1.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../node1.h -o moc_node1.cpp

moc_node2.cpp: ../../node2.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../node2.h -o moc_node2.cpp

moc_node3.cpp: ../../node3.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../node3.h -o moc_node3.cpp

moc_setting.cpp: ../../setting.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../taskserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../setting.h -o moc_setting.cpp

moc_taskmqtt.cpp: ../../taskmqtt.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../taskmqtt.h -o moc_taskmqtt.cpp

moc_taskserialport.cpp: ../../taskserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../taskserialport.h -o moc_taskserialport.cpp

moc_taskwarning.cpp: ../../taskwarning.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../taskwarning.h -o moc_taskwarning.cpp

moc_widget.cpp: ../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../widget.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../widget.h -o moc_widget.cpp

moc_qmqtt_client.cpp: ../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_client.h -o moc_qmqtt_client.cpp

moc_qmqtt_network_p.cpp: ../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_network_p.h -o moc_qmqtt_network_p.cpp

moc_qmqtt_networkinterface.cpp: ../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_networkinterface.h -o moc_qmqtt_networkinterface.cpp

moc_qmqtt_router.cpp: ../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_router.h -o moc_qmqtt_router.cpp

moc_qmqtt_routesubscription.cpp: ../../mqtt/qmqtt_routesubscription.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QRegularExpression \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregularexpression.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QStringList \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_routesubscription.h -o moc_qmqtt_routesubscription.cpp

moc_qmqtt_socket_p.cpp: ../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_socket_p.h -o moc_qmqtt_socket_p.cpp

moc_qmqtt_socketinterface.cpp: ../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_socketinterface.h -o moc_qmqtt_socketinterface.cpp

moc_qmqtt_ssl_socket_p.cpp: ../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_ssl_socket_p.h -o moc_qmqtt_ssl_socket_p.cpp

moc_qmqtt_timer_p.cpp: ../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_timer_p.h -o moc_qmqtt_timer_p.cpp

moc_qmqtt_timerinterface.cpp: ../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_timerinterface.h -o moc_qmqtt_timerinterface.cpp

moc_qmqtt_websocket_p.cpp: ../../mqtt/qmqtt_websocket_p.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_websocket_p.h -o moc_qmqtt_websocket_p.cpp

moc_qmqtt_websocketiodevice_p.cpp: ../../mqtt/qmqtt_websocketiodevice_p.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QIODevice \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		moc_predefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/moc $(DEFINES) --include /home/<USER>/Qtproject/Loong_disp/Loong_disp/build/Desktop_Qt_5_15_2_GCC_64bit-Debug/moc_predefs.h -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/Qtproject/Loong_disp/Loong_disp -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork -I/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore -I. -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include ../../mqtt/qmqtt_websocketiodevice_p.h -o moc_qmqtt_websocketiodevice_p.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_node1.h ui_node2.h ui_node3.h ui_setting.h ui_widget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_node1.h ui_node2.h ui_node3.h ui_setting.h ui_widget.h
ui_node1.h: ../../node1.ui \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic ../../node1.ui -o ui_node1.h

ui_node2.h: ../../node2.ui \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic ../../node2.ui -o ui_node2.h

ui_node3.h: ../../node3.ui \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic ../../node3.ui -o ui_node3.h

ui_setting.h: ../../setting.ui \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic ../../setting.ui -o ui_setting.h

ui_widget.h: ../../widget.ui \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic
	/home/<USER>/Qt_5_15/5.15.2/gcc_64/bin/uic ../../widget.ui -o ui_widget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: ../../main.cpp ../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o ../../main.cpp

node1.o: ../../node1.cpp ../../node1.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_node1.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGridLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qicon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QTextEdit \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtextedit.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextdocument.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o node1.o ../../node1.cpp

node2.o: ../../node2.cpp ../../node2.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_node2.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGridLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qicon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QTextEdit \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtextedit.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextdocument.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o node2.o ../../node2.cpp

node3.o: ../../node3.cpp ../../node3.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_node3.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGridLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qicon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QTextEdit \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtextedit.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextdocument.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o node3.o ../../node3.cpp

setting.o: ../../setting.cpp ../../setting.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../taskserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		../../widget.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_setting.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QDoubleSpinBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qspinbox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvalidator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregularexpression.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qicon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o setting.o ../../setting.cpp

taskmqtt.o: ../../taskmqtt.cpp ../../taskmqtt.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o taskmqtt.o ../../taskmqtt.cpp

taskserialport.o: ../../taskserialport.cpp ../../taskserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDebug \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QElapsedTimer
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o taskserialport.o ../../taskserialport.cpp

taskwarning.o: ../../taskwarning.cpp ../../taskwarning.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskmqtt.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o taskwarning.o ../../taskwarning.cpp

widget.o: ../../widget.cpp ../../widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtguiglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtgui-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmargins.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpaintdevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrect.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsize.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpalette.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcolor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgb.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qrgba64.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qbrush.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qmatrix.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpolygon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qregion.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qline.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtransform.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qimage.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixelformat.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpixmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfont.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontmetrics.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qfontinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qcursor.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qkeysequence.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qvector2d.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../mqtt/qmqtt.h \
		../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMessageAuthenticationCode \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChart \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchart.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QAbstractAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qabstractaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QPen \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpen.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QFont \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QLegend \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlegend.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsWidget \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicswidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicslayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainterpath.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/QBrush \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMargins \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QSplineSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qsplineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qlineseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QXYSeries \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qxyseries.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointF \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QChartView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qchartview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGraphicsView \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qpainter.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qtextoption.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qframe.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgraphicsscene.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QDateTimeAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qdatetimeaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/QValueAxis \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCharts/qvalueaxis.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTime \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QThread \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qthread.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qelapsedtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QMessageBox \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qmessagebox.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdialog.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPortInfo \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialportglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/QSerialPort \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtSerialPort/qserialport.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDir \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdir.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfileinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QFileDialog \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../node1.h \
		../../node2.h \
		../../node3.h \
		../../setting.h \
		../../taskserialport.h \
		../../taskwarning.h \
		../../taskmqtt.h \
		ui_widget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QApplication \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qeventloop.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qguiapplication.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qinputmethod.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QGridLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qgridlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qboxlayout.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QHBoxLayout \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QLabel \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qlabel.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QPushButton \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qpushbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/qabstractbutton.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtGui/qicon.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QSpacerItem \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtWidgets/QVBoxLayout
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o widget.o ../../widget.cpp

qmqtt_client.o: ../../mqtt/qmqtt_client.cpp ../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		../../mqtt/qmqtt_client_p.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QHash \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_client.o ../../mqtt/qmqtt_client.cpp

qmqtt_client_p.o: ../../mqtt/qmqtt_client_p.cpp ../../mqtt/qmqtt_client_p.h \
		../../mqtt/qmqtt_client.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QHash \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_frame.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		../../mqtt/qmqtt_message.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QLoggingCategory \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qloggingcategory.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QUuid \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/quuid.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFile \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfile.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qfiledevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslKey \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslkey.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_client_p.o ../../mqtt/qmqtt_client_p.cpp

qmqtt_frame.o: ../../mqtt/qmqtt_frame.cpp ../../mqtt/qmqtt_frame.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QLoggingCategory \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qloggingcategory.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDataStream \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_frame.o ../../mqtt/qmqtt_frame.cpp

qmqtt_message.o: ../../mqtt/qmqtt_message.cpp ../../mqtt/qmqtt_message.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../mqtt/qmqtt_message_p.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedData
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_message.o ../../mqtt/qmqtt_message.cpp

qmqtt_network.o: ../../mqtt/qmqtt_network.cpp ../../mqtt/qmqtt_network_p.h \
		../../mqtt/qmqtt_networkinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h \
		../../mqtt/qmqtt_websocket_p.h \
		../../mqtt/qmqtt_frame.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QDataStream \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatastream.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_network.o ../../mqtt/qmqtt_network.cpp

qmqtt_router.o: ../../mqtt/qmqtt_router.cpp ../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		../../mqtt/qmqtt_routesubscription.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QRegularExpression \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregularexpression.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QStringList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QLoggingCategory \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qloggingcategory.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_router.o ../../mqtt/qmqtt_router.cpp

qmqtt_routesubscription.o: ../../mqtt/qmqtt_routesubscription.cpp ../../mqtt/qmqtt_routesubscription.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QRegularExpression \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregularexpression.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QStringList \
		../../mqtt/qmqtt_router.h \
		../../mqtt/qmqtt_client.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		../../mqtt/qmqtt_routedmessage.h \
		../../mqtt/qmqtt_message.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QMetaType \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QHash \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QLoggingCategory \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qloggingcategory.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QLatin1String \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QLatin1Char \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QRegularExpressionMatch
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_routesubscription.o ../../mqtt/qmqtt_routesubscription.cpp

qmqtt_socket.o: ../../mqtt/qmqtt_socket.cpp ../../mqtt/qmqtt_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QTcpSocket
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_socket.o ../../mqtt/qmqtt_socket.cpp

qmqtt_ssl_socket.o: ../../mqtt/qmqtt_ssl_socket.cpp ../../mqtt/qmqtt_ssl_socket_p.h \
		../../mqtt/qmqtt_socketinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QScopedPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslError
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_ssl_socket.o ../../mqtt/qmqtt_ssl_socket.cpp

qmqtt_timer.o: ../../mqtt/qmqtt_timer.cpp ../../mqtt/qmqtt_timer_p.h \
		../../mqtt/qmqtt_timerinterface.h \
		../../mqtt/qmqtt_global.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QtGlobal \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QTimer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtimer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasictimer.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_timer.o ../../mqtt/qmqtt_timer.cpp

qmqtt_websocket.o: ../../mqtt/qmqtt_websocket.cpp ../../mqtt/qmqtt_websocket_p.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QObject \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QHostAddress \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qhostaddress.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetworkglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtnetwork-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qshareddata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qabstractsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdebug.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtextstream.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlocale.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvariant.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qset.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontiguouscache.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QString \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QList \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QAbstractSocket \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslConfiguration \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslconfiguration.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qtcpsocket.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslerror.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qsslcertificate.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcryptographichash.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qdatetime.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qssl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QFlags \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QNetworkRequest \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/qnetworkrequest.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QSharedDataPointer \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QUrl \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qurlquery.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QVariant \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtNetwork/QSslError
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_websocket.o ../../mqtt/qmqtt_websocket.cpp

qmqtt_websocketiodevice.o: ../../mqtt/qmqtt_websocketiodevice.cpp ../../mqtt/qmqtt_websocketiodevice_p.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QByteArray \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qrefcount.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobal.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qconfig.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtcore-config.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsystemdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qprocessordetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcompilerdetection.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qtypeinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qsysinfo.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlogging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qflags.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qglobalstatic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmutex.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnumeric.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qversiontagging.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbasicatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qgenericatomic.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qatomic_msvc.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qnamespace.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qarraydata.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/QIODevice \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiodevice.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstring.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qchar.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringliteral.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringview.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringbuilder.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qalgorithms.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qiterator.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qhashfunctions.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpair.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvector.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainertools_impl.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qpoint.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qbytearraylist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringlist.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qregexp.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qstringmatcher.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcoreevent.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qscopedpointer.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qmetatype.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qvarlengtharray.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qcontainerfwd.h \
		/home/<USER>/Qt_5_15/5.15.2/gcc_64/include/QtCore/qobject_impl.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qmqtt_websocketiodevice.o ../../mqtt/qmqtt_websocketiodevice.cpp

qrc_pic.o: qrc_pic.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o qrc_pic.o qrc_pic.cpp

moc_node1.o: moc_node1.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_node1.o moc_node1.cpp

moc_node2.o: moc_node2.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_node2.o moc_node2.cpp

moc_node3.o: moc_node3.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_node3.o moc_node3.cpp

moc_setting.o: moc_setting.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_setting.o moc_setting.cpp

moc_taskmqtt.o: moc_taskmqtt.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_taskmqtt.o moc_taskmqtt.cpp

moc_taskserialport.o: moc_taskserialport.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_taskserialport.o moc_taskserialport.cpp

moc_taskwarning.o: moc_taskwarning.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_taskwarning.o moc_taskwarning.cpp

moc_widget.o: moc_widget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_widget.o moc_widget.cpp

moc_qmqtt_client.o: moc_qmqtt_client.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_client.o moc_qmqtt_client.cpp

moc_qmqtt_network_p.o: moc_qmqtt_network_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_network_p.o moc_qmqtt_network_p.cpp

moc_qmqtt_networkinterface.o: moc_qmqtt_networkinterface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_networkinterface.o moc_qmqtt_networkinterface.cpp

moc_qmqtt_router.o: moc_qmqtt_router.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_router.o moc_qmqtt_router.cpp

moc_qmqtt_routesubscription.o: moc_qmqtt_routesubscription.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_routesubscription.o moc_qmqtt_routesubscription.cpp

moc_qmqtt_socket_p.o: moc_qmqtt_socket_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_socket_p.o moc_qmqtt_socket_p.cpp

moc_qmqtt_socketinterface.o: moc_qmqtt_socketinterface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_socketinterface.o moc_qmqtt_socketinterface.cpp

moc_qmqtt_ssl_socket_p.o: moc_qmqtt_ssl_socket_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_ssl_socket_p.o moc_qmqtt_ssl_socket_p.cpp

moc_qmqtt_timer_p.o: moc_qmqtt_timer_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_timer_p.o moc_qmqtt_timer_p.cpp

moc_qmqtt_timerinterface.o: moc_qmqtt_timerinterface.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_timerinterface.o moc_qmqtt_timerinterface.cpp

moc_qmqtt_websocket_p.o: moc_qmqtt_websocket_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_websocket_p.o moc_qmqtt_websocket_p.cpp

moc_qmqtt_websocketiodevice_p.o: moc_qmqtt_websocketiodevice_p.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_qmqtt_websocketiodevice_p.o moc_qmqtt_websocketiodevice_p.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/Loong_disp/bin || mkdir -p $(INSTALL_ROOT)/opt/Loong_disp/bin
	$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT)/opt/Loong_disp/bin/$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT)/opt/Loong_disp/bin/$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/Loong_disp/bin/ 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

