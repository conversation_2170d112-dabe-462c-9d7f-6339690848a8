var _ubaParams=_ubaParams||{};_ubaParams.cloudBIType=2;_ubaParams.download;_ubaParams.download_suc=1;_ubaParams.Num2=2;_ubaParams.Num4=4;_ubaParams.sdkGaUpload="2";_ubaParams.biReqQueue=_ubaParams.biReqQueue||[];_ubaParams.gaReqQueue=_ubaParams.gaReqQueue||[];_ubaParams.isGAReady=false;_ubaParams.isGAUpload=false;_ubaParams.userAccount;_ubaParams.pageViewID=_ubaParams.pageViewID||guidGenerator();_ubaParams.bi_C1_20=["C1","C2","C3","C4","C5","C6","C7","C8","C9","C10","C11","C12","C13","C14","C15","C16","C17","C18","C19","C20"];_ubaParams.bi_D1_20=["D1","D2","D3","D4","D5","D6","D7","D8","D9","D10","D11","D12","D13","D14","D15","D16","D17","D18","D19","D20"];_ubaParams.timinhObj=window.performance?window.performance.timing:"";_ubaParams.memberID="";_ubaParams.siteId="";_ubaParams.companyID="";_ubaParams.downloadHeat;_ubaParams.downloadHeat_suc=1;var UBA_COUNTRY_CODES=["TH","TR"];function guidGenerator(){var d=(new Date).getTime();var uuid="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){var r=(d+Math.random()*16)%16|0;d=Math.floor(d/16);return(c=="x"?r:r&3|8).toString(16)});return uuid}if(!Array.prototype.indexOf){Array.prototype.indexOf=function(val){var value=this;for(var i=0;i<value.length;i++){if(value[i]==val)return i}return-1}}var JSLoader=function(){var callbacks=[];var loaded=false;function executeCallBack(){var len=callbacks.length;for(var i=0;i<len;i++){var fun=callbacks.shift();fun()}}function getScript(url){var script=document.createElement("script");script.type="text/javascript";script.async="async";script.src=url;document.body.appendChild(script);if(script.readyState){script.onreadystatechange=function(){if(script.readyState=="complete"||script.readyState=="loaded"){script.onreadystatechange=null;loaded=true;executeCallBack()}}}else{script.onload=function(){loaded=true;executeCallBack()}}}return{load:function(url){var arg=arguments;var len=arg.length;getScript(url);for(var i=1;i<len;i++){var fun=arg[i];if(typeof fun=="function"){if(loaded){fun()}else{callbacks.push(fun)}}}}}}();function getUBACookieValue(name){var name=encodeURI(name);try{var allcookies=document.cookie;name+="=";var pos=allcookies.indexOf(name);if(pos!=-1){var start=pos+name.length;var end=allcookies.indexOf(";",start);if(end==-1)end=allcookies.length;var value=allcookies.substring(start,end);return decodeURI(value)}else{return""}}catch(e){return""}}function getUBASendFlag(){var sendFlag=false;try{if(_ubaParams&&_ubaParams.siteId==="intl"||window&&window.location&&window.location.host.indexOf("intl.huaweicloud.com")>-1||window&&window.location&&window.location.href&&window.location.href.indexOf(".huaweicloud.com/intl")>-1){sendFlag=true;var countrycode=getUBACookieValue("uba_countrycode");if(!countrycode){sendFlag=false}else{var privacyDenyStr=getUBACookieValue("privacyDeny");if(privacyDenyStr){var privacyDeny=JSON.parse(privacyDenyStr);if(privacyDeny.analytics){sendFlag=false}}if(UBA_COUNTRY_CODES.indexOf(countrycode)>-1){sendFlag=false;var essentialStr=getUBACookieValue("essential");var analyticsStr=getUBACookieValue("Analytics");var advertisingStr=getUBACookieValue("Advertising");if(essentialStr=="active"&&analyticsStr=="active"&&advertisingStr=="active"){sendFlag=true}var hwcp_eu=getUBACookieValue("hwcp_eu");if(hwcp_eu){var euObj=JSON.parse(hwcp_eu);if(euObj.analysis==1&&euObj.ad==1&&euObj.essential==1){sendFlag=true}}}var hwcp_intl=getUBACookieValue("hwcp_intl");if(hwcp_intl){var intlObj=JSON.parse(hwcp_intl);if(intlObj.analysis===1&&intlObj.ad===1&&intlObj.essential===1){sendFlag=true}else if(ntlObj.analysis===0||intlObj.ad===0){sendFlag=false}}}}}catch(err){}return sendFlag}function isNeedWait(){var result=false;if(window&&window.location&&window.location.href&&(window.location.href.indexOf("www.huaweicloud.com/intl/")>-1||window.location.href.indexOf("activity.huaweicloud.com/intl/")>-1)){result=true}return result}function init(statType,appKey,gaid,jsonParam){if(arguments.length!=_ubaParams.Num4){return}if(!(jsonParam.UserAccount&&typeof appKey=="string")){return}if(!location){return}if(location.pathname&&location.pathname.indexOf("/eu/")===0||location.hostname&&location.hostname.indexOf(".eu.huaweicloud.com")>-1||location.pathname&&location.pathname.indexOf("/intl/ar-mena/")===0){return}if(jsonParam&&jsonParam.siteId){_ubaParams.siteId=jsonParam.siteId}if(jsonParam&&jsonParam.companyID){_ubaParams.companyID=jsonParam.companyID}if(jsonParam&&jsonParam.IAMUserID){_ubaParams.IAMUserID=jsonParam.IAMUserID}_ubaParams.memberID=jsonParam.MemberID||"";_ubaParams.userAccount=jsonParam.UserAccount;_ubaParams.bi_ga_appkey=appKey;if(_ubaParams.download===_ubaParams.download_suc){_initCloudBI(appKey,jsonParam)}else{JSLoader.load("https://res.hc-cdn.com/bi/uba.in.js?v=2.50",function(){_initCloudBI&&_initCloudBI(appKey,jsonParam);_ubaParams.download=_ubaParams.download_suc;if(isLoadStarted()||isNeedWait()){_ubaParams.ReqQueueSend(_ubaParams.biReqQueue)}else{window.addEventListener("load",function(event){_ubaParams.ReqQueueSend(_ubaParams.biReqQueue)})}})}if(!getUBASendFlag()){return}if(!(gaid&&typeof gaid=="string")||statType==1){return}else{_ubaParams.isGAUpload=true}(function(i,s,o,g,r,a,m){i["GoogleAnalyticsObject"]=r;i[r]=i[r]||function(){(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date;a=s.createElement(o),m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)})(window,document,"script","https://res.hc-cdn.com/bi/analytics.js?v=2.1","ga");ga("create",gaid,"auto");ga("set","anonymizeIp",true);_ubaParams.isGAReady=true;if(isLoadStarted()){_ubaParams.ReqQueueSend(_ubaParams.gaReqQueue)}else{window.addEventListener("load",function(event){_ubaParams.ReqQueueSend(_ubaParams.gaReqQueue)})}}_ubaParams.ReqQueueSend=function(ReqQueue){if(ReqQueue&&ReqQueue.length>0){while(ReqQueue.length>0){var req=ReqQueue.pop();if(typeof req=="object"){if(req.type=="event"){if(req.jp){req.jp.transport="beacon"}ga("send","event",req.ec,req.ea,req.el,req.ev,req.jp)}else if(req.type=="pageview"){if(req.jp){req.jp.transport="beacon"}ga("send","pageview",req.page,req.jp)}else if(req.type=="sendPageView"){_sendPageView(req.page,req.jp)}else if(req.type=="sendEvent"){_sendEvent(req.en,req.ec,req.ea,req.el,req.ev,req.jp)}}}}};var initCBISDK=init;function isLoadStarted(){var flag=true;if(_ubaParams.timinhObj){if(_ubaParams.timinhObj.loadEventStart){flag=true}else{flag=false}}else{flag=true}return flag}function onEvent(eventCategory,eventAction,eventLabel,eventValue,jsonParam){if(arguments.length<_ubaParams.Num2){return}if(!(eventCategory&&typeof eventCategory=="string")){return}if(!(eventAction&&typeof eventAction=="string")){return}var eventName=eventCategory+"_"+eventAction;if(jsonParam&&jsonParam.eventPrivacyName){eventName=jsonParam.eventPrivacyName}if(_ubaParams.download==_ubaParams.download_suc&&isLoadStarted()){_sendEvent&&_sendEvent(eventName,eventCategory,eventAction,eventLabel,eventValue,jsonParam)}else if(_ubaParams.download==_ubaParams.download_suc&&!isLoadStarted()){window.addEventListener("load",function(event){_sendEvent&&_sendEvent(eventName,eventCategory,eventAction,eventLabel,eventValue,jsonParam)})}else{_ubaParams.biReqQueue.push({type:"sendEvent",en:eventName,ec:eventCategory,ea:eventAction,el:eventLabel,ev:eventValue,jp:jsonParam})}if(!_ubaParams.isGAUpload){return}if(!getUBASendFlag()){return}var ga_event_value;if(eventValue&&typeof eventValue=="number"){ga_event_value=eventValue}else if(eventValue&&typeof eventValue=="string"&&eventValue.indexOf("success")!=-1){ga_event_value=1}else{ga_event_value=0}var newJson={};var transferKey="";if(typeof jsonParam=="object"&&Object.prototype.toString.call(jsonParam).toLowerCase()=="[object object]"&&!jsonParam.length){for(var key in jsonParam){if(_ubaParams.bi_C1_20.indexOf(key)>-1){transferKey=key.replace("C","metric");newJson[transferKey]=jsonParam[key]}else if(_ubaParams.bi_D1_20.indexOf(key)>-1){transferKey=key.replace("D","dimension");newJson[transferKey]=jsonParam[key]}}}newJson["dimension1"]=_ubaParams.userAccount;_ubaParams.bi_ga_appkey_ex=jsonParam?jsonParam["AppKey"]||jsonParam["Appkey"]||_ubaParams.bi_ga_appkey:_ubaParams.bi_ga_appkey;if(_ubaParams.isGAReady&&isLoadStarted()){newJson.transport="beacon";ga("send","event",eventCategory,eventAction,eventLabel,ga_event_value,newJson)}else{_ubaParams.gaReqQueue.push({type:"event",ec:eventCategory,ea:eventAction,el:eventLabel,ev:ga_event_value,jp:newJson})}}function onPageView(page,jsonParam){if(_ubaParams.download==_ubaParams.download_suc&&isLoadStarted()){_sendPageView(page,jsonParam)}else{_ubaParams.biReqQueue.push({type:"sendPageView",page:page,jp:jsonParam})}if(!_ubaParams.isGAUpload){return}var newJson={};var transferKey="";if(typeof jsonParam=="object"&&Object.prototype.toString.call(jsonParam).toLowerCase()=="[object object]"&&!jsonParam.length){for(var key in jsonParam){if(_ubaParams.bi_C1_20.indexOf(key)>-1){transferKey=key.replace("C","metric");newJson[transferKey]=jsonParam[key]}else if(_ubaParams.bi_D1_20.indexOf(key)>-1){transferKey=key.replace("D","dimension");newJson[transferKey]=jsonParam[key]}}newJson["dimension3"]=jsonParam["subPage"]}newJson["dimension1"]=_ubaParams.userAccount;_ubaParams.bi_ga_appkey_ex=jsonParam?jsonParam["AppKey"]||jsonParam["Appkey"]||_ubaParams.bi_ga_appkey:_ubaParams.bi_ga_appkey;if(_ubaParams.isGAReady&&isLoadStarted()){newJson.transport="beacon";ga("send","pageview",page?page:location.pathname+location.hash,newJson)}else{_ubaParams.gaReqQueue.push({type:"pageview",page:page?page:location.pathname+location.hash,jp:newJson})}}function onDmpa(userId,jsonParam){}function onException(exceptionMessage,exceptionStack,jsonParam){}