/*! For license information please see furion-cdn.min.js.LICENSE.txt */
!function(){var A={"./node_modules/core-js/es/array/filter.js":function(A,e,t){"use strict";t("./node_modules/core-js/modules/es.array.filter.js");var n=t("./node_modules/core-js/internals/entry-unbind.js");A.exports=n("Array","filter")},"./node_modules/core-js/es/array/find.js":function(A,e,t){"use strict";t("./node_modules/core-js/modules/es.array.find.js");var n=t("./node_modules/core-js/internals/entry-unbind.js");A.exports=n("Array","find")},"./node_modules/core-js/es/array/includes.js":function(A,e,t){"use strict";t("./node_modules/core-js/modules/es.array.includes.js");var n=t("./node_modules/core-js/internals/entry-unbind.js");A.exports=n("Array","includes")},"./node_modules/core-js/es/string/includes.js":function(A,e,t){"use strict";t("./node_modules/core-js/modules/es.string.includes.js");var n=t("./node_modules/core-js/internals/entry-unbind.js");A.exports=n("String","includes")},"./node_modules/core-js/internals/a-callable.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-callable.js"),r=t("./node_modules/core-js/internals/try-to-string.js"),o=TypeError;A.exports=function(A){if(n(A))return A;throw new o(r(A)+" is not a function")}},"./node_modules/core-js/internals/add-to-unscopables.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/well-known-symbol.js"),r=t("./node_modules/core-js/internals/object-create.js"),o=t("./node_modules/core-js/internals/object-define-property.js").f,i=n("unscopables"),s=Array.prototype;void 0===s[i]&&o(s,i,{configurable:!0,value:r(null)}),A.exports=function(A){s[i][A]=!0}},"./node_modules/core-js/internals/an-object.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-object.js"),r=String,o=TypeError;A.exports=function(A){if(n(A))return A;throw new o(r(A)+" is not an object")}},"./node_modules/core-js/internals/array-includes.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/to-indexed-object.js"),r=t("./node_modules/core-js/internals/to-absolute-index.js"),o=t("./node_modules/core-js/internals/length-of-array-like.js"),i=function(A){return function(e,t,i){var s=n(e),a=o(s);if(0===a)return!A&&-1;var c,u=r(i,a);if(A&&t!=t){for(;a>u;)if((c=s[u++])!=c)return!0}else for(;a>u;u++)if((A||u in s)&&s[u]===t)return A||u||0;return!A&&-1}};A.exports={includes:i(!0),indexOf:i(!1)}},"./node_modules/core-js/internals/array-iteration.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-bind-context.js"),r=t("./node_modules/core-js/internals/function-uncurry-this.js"),o=t("./node_modules/core-js/internals/indexed-object.js"),i=t("./node_modules/core-js/internals/to-object.js"),s=t("./node_modules/core-js/internals/length-of-array-like.js"),a=t("./node_modules/core-js/internals/array-species-create.js"),c=r([].push),u=function(A){var e=1===A,t=2===A,r=3===A,u=4===A,l=6===A,d=7===A,B=5===A||l;return function(g,f,p,h){for(var w,C,Q=i(g),m=o(Q),U=s(m),v=n(f,p),y=0,F=h||a,I=e?F(g,U):t||d?F(g,0):void 0;U>y;y++)if((B||y in m)&&(C=v(w=m[y],y,Q),A))if(e)I[y]=C;else if(C)switch(A){case 3:return!0;case 5:return w;case 6:return y;case 2:c(I,w)}else switch(A){case 4:return!1;case 7:c(I,w)}return l?-1:r||u?u:I}};A.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},"./node_modules/core-js/internals/array-method-has-species-support.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/fails.js"),r=t("./node_modules/core-js/internals/well-known-symbol.js"),o=t("./node_modules/core-js/internals/environment-v8-version.js"),i=r("species");A.exports=function(A){return o>=51||!n((function(){var e=[];return(e.constructor={})[i]=function(){return{foo:1}},1!==e[A](Boolean).foo}))}},"./node_modules/core-js/internals/array-species-constructor.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-array.js"),r=t("./node_modules/core-js/internals/is-constructor.js"),o=t("./node_modules/core-js/internals/is-object.js"),i=t("./node_modules/core-js/internals/well-known-symbol.js")("species"),s=Array;A.exports=function(A){var e;return n(A)&&(e=A.constructor,(r(e)&&(e===s||n(e.prototype))||o(e)&&null===(e=e[i]))&&(e=void 0)),void 0===e?s:e}},"./node_modules/core-js/internals/array-species-create.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/array-species-constructor.js");A.exports=function(A,e){return new(n(A))(0===e?0:e)}},"./node_modules/core-js/internals/classof-raw.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=n({}.toString),o=n("".slice);A.exports=function(A){return o(r(A),8,-1)}},"./node_modules/core-js/internals/classof.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/to-string-tag-support.js"),r=t("./node_modules/core-js/internals/is-callable.js"),o=t("./node_modules/core-js/internals/classof-raw.js"),i=t("./node_modules/core-js/internals/well-known-symbol.js")("toStringTag"),s=Object,a="Arguments"===o(function(){return arguments}());A.exports=n?o:function(A){var e,t,n;return void 0===A?"Undefined":null===A?"Null":"string"==typeof(t=function(A,e){try{return A[e]}catch(A){}}(e=s(A),i))?t:a?o(e):"Object"===(n=o(e))&&r(e.callee)?"Arguments":n}},"./node_modules/core-js/internals/copy-constructor-properties.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/has-own-property.js"),r=t("./node_modules/core-js/internals/own-keys.js"),o=t("./node_modules/core-js/internals/object-get-own-property-descriptor.js"),i=t("./node_modules/core-js/internals/object-define-property.js");A.exports=function(A,e,t){for(var s=r(e),a=i.f,c=o.f,u=0;u<s.length;u++){var l=s[u];n(A,l)||t&&n(t,l)||a(A,l,c(e,l))}}},"./node_modules/core-js/internals/correct-is-regexp-logic.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/well-known-symbol.js")("match");A.exports=function(A){var e=/./;try{"/./"[A](e)}catch(t){try{return e[n]=!1,"/./"[A](e)}catch(A){}}return!1}},"./node_modules/core-js/internals/create-non-enumerable-property.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/object-define-property.js"),o=t("./node_modules/core-js/internals/create-property-descriptor.js");A.exports=n?function(A,e,t){return r.f(A,e,o(1,t))}:function(A,e,t){return A[e]=t,A}},"./node_modules/core-js/internals/create-property-descriptor.js":function(A){"use strict";A.exports=function(A,e){return{enumerable:!(1&A),configurable:!(2&A),writable:!(4&A),value:e}}},"./node_modules/core-js/internals/define-built-in.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-callable.js"),r=t("./node_modules/core-js/internals/object-define-property.js"),o=t("./node_modules/core-js/internals/make-built-in.js"),i=t("./node_modules/core-js/internals/define-global-property.js");A.exports=function(A,e,t,s){s||(s={});var a=s.enumerable,c=void 0!==s.name?s.name:e;if(n(t)&&o(t,c,s),s.global)a?A[e]=t:i(e,t);else{try{s.unsafe?A[e]&&(a=!0):delete A[e]}catch(A){}a?A[e]=t:r.f(A,e,{value:t,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return A}},"./node_modules/core-js/internals/define-global-property.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=Object.defineProperty;A.exports=function(A,e){try{r(n,A,{value:e,configurable:!0,writable:!0})}catch(t){n[A]=e}return e}},"./node_modules/core-js/internals/descriptors.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/fails.js");A.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},"./node_modules/core-js/internals/document-create-element.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=t("./node_modules/core-js/internals/is-object.js"),o=n.document,i=r(o)&&r(o.createElement);A.exports=function(A){return i?o.createElement(A):{}}},"./node_modules/core-js/internals/entry-unbind.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=t("./node_modules/core-js/internals/function-uncurry-this.js");A.exports=function(A,e){return r(n[A].prototype[e])}},"./node_modules/core-js/internals/enum-bug-keys.js":function(A){"use strict";A.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"./node_modules/core-js/internals/environment-user-agent.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js").navigator,r=n&&n.userAgent;A.exports=r?String(r):""},"./node_modules/core-js/internals/environment-v8-version.js":function(A,e,t){"use strict";var n,r,o=t("./node_modules/core-js/internals/global-this.js"),i=t("./node_modules/core-js/internals/environment-user-agent.js"),s=o.process,a=o.Deno,c=s&&s.versions||a&&a.version,u=c&&c.v8;u&&(r=(n=u.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!r&&i&&(!(n=i.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=i.match(/Chrome\/(\d+)/))&&(r=+n[1]),A.exports=r},"./node_modules/core-js/internals/export.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=t("./node_modules/core-js/internals/object-get-own-property-descriptor.js").f,o=t("./node_modules/core-js/internals/create-non-enumerable-property.js"),i=t("./node_modules/core-js/internals/define-built-in.js"),s=t("./node_modules/core-js/internals/define-global-property.js"),a=t("./node_modules/core-js/internals/copy-constructor-properties.js"),c=t("./node_modules/core-js/internals/is-forced.js");A.exports=function(A,e){var t,u,l,d,B,g=A.target,f=A.global,p=A.stat;if(t=f?n:p?n[g]||s(g,{}):n[g]&&n[g].prototype)for(u in e){if(d=e[u],l=A.dontCallGetSet?(B=r(t,u))&&B.value:t[u],!c(f?u:g+(p?".":"#")+u,A.forced)&&void 0!==l){if(typeof d==typeof l)continue;a(d,l)}(A.sham||l&&l.sham)&&o(d,"sham",!0),i(t,u,d,A)}}},"./node_modules/core-js/internals/fails.js":function(A){"use strict";A.exports=function(A){try{return!!A()}catch(A){return!0}}},"./node_modules/core-js/internals/function-bind-context.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this-clause.js"),r=t("./node_modules/core-js/internals/a-callable.js"),o=t("./node_modules/core-js/internals/function-bind-native.js"),i=n(n.bind);A.exports=function(A,e){return r(A),void 0===e?A:o?i(A,e):function(){return A.apply(e,arguments)}}},"./node_modules/core-js/internals/function-bind-native.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/fails.js");A.exports=!n((function(){var A=function(){}.bind();return"function"!=typeof A||A.hasOwnProperty("prototype")}))},"./node_modules/core-js/internals/function-call.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-bind-native.js"),r=Function.prototype.call;A.exports=n?r.bind(r):function(){return r.apply(r,arguments)}},"./node_modules/core-js/internals/function-name.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/has-own-property.js"),o=Function.prototype,i=n&&Object.getOwnPropertyDescriptor,s=r(o,"name"),a=s&&"something"===function(){}.name,c=s&&(!n||n&&i(o,"name").configurable);A.exports={EXISTS:s,PROPER:a,CONFIGURABLE:c}},"./node_modules/core-js/internals/function-uncurry-this-clause.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/classof-raw.js"),r=t("./node_modules/core-js/internals/function-uncurry-this.js");A.exports=function(A){if("Function"===n(A))return r(A)}},"./node_modules/core-js/internals/function-uncurry-this.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-bind-native.js"),r=Function.prototype,o=r.call,i=n&&r.bind.bind(o,o);A.exports=n?i:function(A){return function(){return o.apply(A,arguments)}}},"./node_modules/core-js/internals/get-built-in.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=t("./node_modules/core-js/internals/is-callable.js");A.exports=function(A,e){return arguments.length<2?(t=n[A],r(t)?t:void 0):n[A]&&n[A][e];var t}},"./node_modules/core-js/internals/get-method.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/a-callable.js"),r=t("./node_modules/core-js/internals/is-null-or-undefined.js");A.exports=function(A,e){var t=A[e];return r(t)?void 0:n(t)}},"./node_modules/core-js/internals/global-this.js":function(A,e,t){"use strict";var n=function(A){return A&&A.Math===Math&&A};A.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof t.g&&t.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},"./node_modules/core-js/internals/has-own-property.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=t("./node_modules/core-js/internals/to-object.js"),o=n({}.hasOwnProperty);A.exports=Object.hasOwn||function(A,e){return o(r(A),e)}},"./node_modules/core-js/internals/hidden-keys.js":function(A){"use strict";A.exports={}},"./node_modules/core-js/internals/html.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/get-built-in.js");A.exports=n("document","documentElement")},"./node_modules/core-js/internals/ie8-dom-define.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/fails.js"),o=t("./node_modules/core-js/internals/document-create-element.js");A.exports=!n&&!r((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},"./node_modules/core-js/internals/indexed-object.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=t("./node_modules/core-js/internals/fails.js"),o=t("./node_modules/core-js/internals/classof-raw.js"),i=Object,s=n("".split);A.exports=r((function(){return!i("z").propertyIsEnumerable(0)}))?function(A){return"String"===o(A)?s(A,""):i(A)}:i},"./node_modules/core-js/internals/inspect-source.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=t("./node_modules/core-js/internals/is-callable.js"),o=t("./node_modules/core-js/internals/shared-store.js"),i=n(Function.toString);r(o.inspectSource)||(o.inspectSource=function(A){return i(A)}),A.exports=o.inspectSource},"./node_modules/core-js/internals/internal-state.js":function(A,e,t){"use strict";var n,r,o,i=t("./node_modules/core-js/internals/weak-map-basic-detection.js"),s=t("./node_modules/core-js/internals/global-this.js"),a=t("./node_modules/core-js/internals/is-object.js"),c=t("./node_modules/core-js/internals/create-non-enumerable-property.js"),u=t("./node_modules/core-js/internals/has-own-property.js"),l=t("./node_modules/core-js/internals/shared-store.js"),d=t("./node_modules/core-js/internals/shared-key.js"),B=t("./node_modules/core-js/internals/hidden-keys.js"),g="Object already initialized",f=s.TypeError,p=s.WeakMap;if(i||l.state){var h=l.state||(l.state=new p);h.get=h.get,h.has=h.has,h.set=h.set,n=function(A,e){if(h.has(A))throw new f(g);return e.facade=A,h.set(A,e),e},r=function(A){return h.get(A)||{}},o=function(A){return h.has(A)}}else{var w=d("state");B[w]=!0,n=function(A,e){if(u(A,w))throw new f(g);return e.facade=A,c(A,w,e),e},r=function(A){return u(A,w)?A[w]:{}},o=function(A){return u(A,w)}}A.exports={set:n,get:r,has:o,enforce:function(A){return o(A)?r(A):n(A,{})},getterFor:function(A){return function(e){var t;if(!a(e)||(t=r(e)).type!==A)throw new f("Incompatible receiver, "+A+" required");return t}}}},"./node_modules/core-js/internals/is-array.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/classof-raw.js");A.exports=Array.isArray||function(A){return"Array"===n(A)}},"./node_modules/core-js/internals/is-callable.js":function(A){"use strict";var e="object"==typeof document&&document.all;A.exports=void 0===e&&void 0!==e?function(A){return"function"==typeof A||A===e}:function(A){return"function"==typeof A}},"./node_modules/core-js/internals/is-constructor.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=t("./node_modules/core-js/internals/fails.js"),o=t("./node_modules/core-js/internals/is-callable.js"),i=t("./node_modules/core-js/internals/classof.js"),s=t("./node_modules/core-js/internals/get-built-in.js"),a=t("./node_modules/core-js/internals/inspect-source.js"),c=function(){},u=s("Reflect","construct"),l=/^\s*(?:class|function)\b/,d=n(l.exec),B=!l.test(c),g=function(A){if(!o(A))return!1;try{return u(c,[],A),!0}catch(A){return!1}},f=function(A){if(!o(A))return!1;switch(i(A)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return B||!!d(l,a(A))}catch(A){return!0}};f.sham=!0,A.exports=!u||r((function(){var A;return g(g.call)||!g(Object)||!g((function(){A=!0}))||A}))?f:g},"./node_modules/core-js/internals/is-forced.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/fails.js"),r=t("./node_modules/core-js/internals/is-callable.js"),o=/#|\.prototype\./,i=function(A,e){var t=a[s(A)];return t===u||t!==c&&(r(e)?n(e):!!e)},s=i.normalize=function(A){return String(A).replace(o,".").toLowerCase()},a=i.data={},c=i.NATIVE="N",u=i.POLYFILL="P";A.exports=i},"./node_modules/core-js/internals/is-null-or-undefined.js":function(A){"use strict";A.exports=function(A){return null==A}},"./node_modules/core-js/internals/is-object.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-callable.js");A.exports=function(A){return"object"==typeof A?null!==A:n(A)}},"./node_modules/core-js/internals/is-pure.js":function(A){"use strict";A.exports=!1},"./node_modules/core-js/internals/is-regexp.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-object.js"),r=t("./node_modules/core-js/internals/classof-raw.js"),o=t("./node_modules/core-js/internals/well-known-symbol.js")("match");A.exports=function(A){var e;return n(A)&&(void 0!==(e=A[o])?!!e:"RegExp"===r(A))}},"./node_modules/core-js/internals/is-symbol.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/get-built-in.js"),r=t("./node_modules/core-js/internals/is-callable.js"),o=t("./node_modules/core-js/internals/object-is-prototype-of.js"),i=t("./node_modules/core-js/internals/use-symbol-as-uid.js"),s=Object;A.exports=i?function(A){return"symbol"==typeof A}:function(A){var e=n("Symbol");return r(e)&&o(e.prototype,s(A))}},"./node_modules/core-js/internals/length-of-array-like.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/to-length.js");A.exports=function(A){return n(A.length)}},"./node_modules/core-js/internals/make-built-in.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=t("./node_modules/core-js/internals/fails.js"),o=t("./node_modules/core-js/internals/is-callable.js"),i=t("./node_modules/core-js/internals/has-own-property.js"),s=t("./node_modules/core-js/internals/descriptors.js"),a=t("./node_modules/core-js/internals/function-name.js").CONFIGURABLE,c=t("./node_modules/core-js/internals/inspect-source.js"),u=t("./node_modules/core-js/internals/internal-state.js"),l=u.enforce,d=u.get,B=String,g=Object.defineProperty,f=n("".slice),p=n("".replace),h=n([].join),w=s&&!r((function(){return 8!==g((function(){}),"length",{value:8}).length})),C=String(String).split("String"),Q=A.exports=function(A,e,t){"Symbol("===f(B(e),0,7)&&(e="["+p(B(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),t&&t.getter&&(e="get "+e),t&&t.setter&&(e="set "+e),(!i(A,"name")||a&&A.name!==e)&&(s?g(A,"name",{value:e,configurable:!0}):A.name=e),w&&t&&i(t,"arity")&&A.length!==t.arity&&g(A,"length",{value:t.arity});try{t&&i(t,"constructor")&&t.constructor?s&&g(A,"prototype",{writable:!1}):A.prototype&&(A.prototype=void 0)}catch(A){}var n=l(A);return i(n,"source")||(n.source=h(C,"string"==typeof e?e:"")),A};Function.prototype.toString=Q((function(){return o(this)&&d(this).source||c(this)}),"toString")},"./node_modules/core-js/internals/math-trunc.js":function(A){"use strict";var e=Math.ceil,t=Math.floor;A.exports=Math.trunc||function(A){var n=+A;return(n>0?t:e)(n)}},"./node_modules/core-js/internals/not-a-regexp.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-regexp.js"),r=TypeError;A.exports=function(A){if(n(A))throw new r("The method doesn't accept regular expressions");return A}},"./node_modules/core-js/internals/object-assign.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/function-uncurry-this.js"),o=t("./node_modules/core-js/internals/function-call.js"),i=t("./node_modules/core-js/internals/fails.js"),s=t("./node_modules/core-js/internals/object-keys.js"),a=t("./node_modules/core-js/internals/object-get-own-property-symbols.js"),c=t("./node_modules/core-js/internals/object-property-is-enumerable.js"),u=t("./node_modules/core-js/internals/to-object.js"),l=t("./node_modules/core-js/internals/indexed-object.js"),d=Object.assign,B=Object.defineProperty,g=r([].concat);A.exports=!d||i((function(){if(n&&1!==d({b:1},d(B({},"a",{enumerable:!0,get:function(){B(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var A={},e={},t=Symbol("assign detection"),r="abcdefghijklmnopqrst";return A[t]=7,r.split("").forEach((function(A){e[A]=A})),7!==d({},A)[t]||s(d({},e)).join("")!==r}))?function(A,e){for(var t=u(A),r=arguments.length,i=1,d=a.f,B=c.f;r>i;)for(var f,p=l(arguments[i++]),h=d?g(s(p),d(p)):s(p),w=h.length,C=0;w>C;)f=h[C++],n&&!o(B,p,f)||(t[f]=p[f]);return t}:d},"./node_modules/core-js/internals/object-create.js":function(A,e,t){"use strict";var n,r=t("./node_modules/core-js/internals/an-object.js"),o=t("./node_modules/core-js/internals/object-define-properties.js"),i=t("./node_modules/core-js/internals/enum-bug-keys.js"),s=t("./node_modules/core-js/internals/hidden-keys.js"),a=t("./node_modules/core-js/internals/html.js"),c=t("./node_modules/core-js/internals/document-create-element.js"),u=t("./node_modules/core-js/internals/shared-key.js"),l="prototype",d="script",B=u("IE_PROTO"),g=function(){},f=function(A){return"<"+d+">"+A+"</"+d+">"},p=function(A){A.write(f("")),A.close();var e=A.parentWindow.Object;return A=null,e},h=function(){try{n=new ActiveXObject("htmlfile")}catch(A){}var A,e,t;h="undefined"!=typeof document?document.domain&&n?p(n):(e=c("iframe"),t="java"+d+":",e.style.display="none",a.appendChild(e),e.src=String(t),(A=e.contentWindow.document).open(),A.write(f("document.F=Object")),A.close(),A.F):p(n);for(var r=i.length;r--;)delete h[l][i[r]];return h()};s[B]=!0,A.exports=Object.create||function(A,e){var t;return null!==A?(g[l]=r(A),t=new g,g[l]=null,t[B]=A):t=h(),void 0===e?t:o.f(t,e)}},"./node_modules/core-js/internals/object-define-properties.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/v8-prototype-define-bug.js"),o=t("./node_modules/core-js/internals/object-define-property.js"),i=t("./node_modules/core-js/internals/an-object.js"),s=t("./node_modules/core-js/internals/to-indexed-object.js"),a=t("./node_modules/core-js/internals/object-keys.js");e.f=n&&!r?Object.defineProperties:function(A,e){i(A);for(var t,n=s(e),r=a(e),c=r.length,u=0;c>u;)o.f(A,t=r[u++],n[t]);return A}},"./node_modules/core-js/internals/object-define-property.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/ie8-dom-define.js"),o=t("./node_modules/core-js/internals/v8-prototype-define-bug.js"),i=t("./node_modules/core-js/internals/an-object.js"),s=t("./node_modules/core-js/internals/to-property-key.js"),a=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,l="enumerable",d="configurable",B="writable";e.f=n?o?function(A,e,t){if(i(A),e=s(e),i(t),"function"==typeof A&&"prototype"===e&&"value"in t&&B in t&&!t[B]){var n=u(A,e);n&&n[B]&&(A[e]=t.value,t={configurable:d in t?t[d]:n[d],enumerable:l in t?t[l]:n[l],writable:!1})}return c(A,e,t)}:c:function(A,e,t){if(i(A),e=s(e),i(t),r)try{return c(A,e,t)}catch(A){}if("get"in t||"set"in t)throw new a("Accessors not supported");return"value"in t&&(A[e]=t.value),A}},"./node_modules/core-js/internals/object-get-own-property-descriptor.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/function-call.js"),o=t("./node_modules/core-js/internals/object-property-is-enumerable.js"),i=t("./node_modules/core-js/internals/create-property-descriptor.js"),s=t("./node_modules/core-js/internals/to-indexed-object.js"),a=t("./node_modules/core-js/internals/to-property-key.js"),c=t("./node_modules/core-js/internals/has-own-property.js"),u=t("./node_modules/core-js/internals/ie8-dom-define.js"),l=Object.getOwnPropertyDescriptor;e.f=n?l:function(A,e){if(A=s(A),e=a(e),u)try{return l(A,e)}catch(A){}if(c(A,e))return i(!r(o.f,A,e),A[e])}},"./node_modules/core-js/internals/object-get-own-property-names.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/object-keys-internal.js"),r=t("./node_modules/core-js/internals/enum-bug-keys.js").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(A){return n(A,r)}},"./node_modules/core-js/internals/object-get-own-property-symbols.js":function(A,e){"use strict";e.f=Object.getOwnPropertySymbols},"./node_modules/core-js/internals/object-is-prototype-of.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js");A.exports=n({}.isPrototypeOf)},"./node_modules/core-js/internals/object-keys-internal.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=t("./node_modules/core-js/internals/has-own-property.js"),o=t("./node_modules/core-js/internals/to-indexed-object.js"),i=t("./node_modules/core-js/internals/array-includes.js").indexOf,s=t("./node_modules/core-js/internals/hidden-keys.js"),a=n([].push);A.exports=function(A,e){var t,n=o(A),c=0,u=[];for(t in n)!r(s,t)&&r(n,t)&&a(u,t);for(;e.length>c;)r(n,t=e[c++])&&(~i(u,t)||a(u,t));return u}},"./node_modules/core-js/internals/object-keys.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/object-keys-internal.js"),r=t("./node_modules/core-js/internals/enum-bug-keys.js");A.exports=Object.keys||function(A){return n(A,r)}},"./node_modules/core-js/internals/object-property-is-enumerable.js":function(A,e){"use strict";var t={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,r=n&&!t.call({1:2},1);e.f=r?function(A){var e=n(this,A);return!!e&&e.enumerable}:t},"./node_modules/core-js/internals/ordinary-to-primitive.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-call.js"),r=t("./node_modules/core-js/internals/is-callable.js"),o=t("./node_modules/core-js/internals/is-object.js"),i=TypeError;A.exports=function(A,e){var t,s;if("string"===e&&r(t=A.toString)&&!o(s=n(t,A)))return s;if(r(t=A.valueOf)&&!o(s=n(t,A)))return s;if("string"!==e&&r(t=A.toString)&&!o(s=n(t,A)))return s;throw new i("Can't convert object to primitive value")}},"./node_modules/core-js/internals/own-keys.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/get-built-in.js"),r=t("./node_modules/core-js/internals/function-uncurry-this.js"),o=t("./node_modules/core-js/internals/object-get-own-property-names.js"),i=t("./node_modules/core-js/internals/object-get-own-property-symbols.js"),s=t("./node_modules/core-js/internals/an-object.js"),a=r([].concat);A.exports=n("Reflect","ownKeys")||function(A){var e=o.f(s(A)),t=i.f;return t?a(e,t(A)):e}},"./node_modules/core-js/internals/require-object-coercible.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-null-or-undefined.js"),r=TypeError;A.exports=function(A){if(n(A))throw new r("Can't call method on "+A);return A}},"./node_modules/core-js/internals/shared-key.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/shared.js"),r=t("./node_modules/core-js/internals/uid.js"),o=n("keys");A.exports=function(A){return o[A]||(o[A]=r(A))}},"./node_modules/core-js/internals/shared-store.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/is-pure.js"),r=t("./node_modules/core-js/internals/global-this.js"),o=t("./node_modules/core-js/internals/define-global-property.js"),i="__core-js_shared__",s=A.exports=r[i]||o(i,{});(s.versions||(s.versions=[])).push({version:"3.38.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},"./node_modules/core-js/internals/shared.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/shared-store.js");A.exports=function(A,e){return n[A]||(n[A]=e||{})}},"./node_modules/core-js/internals/symbol-constructor-detection.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/environment-v8-version.js"),r=t("./node_modules/core-js/internals/fails.js"),o=t("./node_modules/core-js/internals/global-this.js").String;A.exports=!!Object.getOwnPropertySymbols&&!r((function(){var A=Symbol("symbol detection");return!o(A)||!(Object(A)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},"./node_modules/core-js/internals/to-absolute-index.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/to-integer-or-infinity.js"),r=Math.max,o=Math.min;A.exports=function(A,e){var t=n(A);return t<0?r(t+e,0):o(t,e)}},"./node_modules/core-js/internals/to-indexed-object.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/indexed-object.js"),r=t("./node_modules/core-js/internals/require-object-coercible.js");A.exports=function(A){return n(r(A))}},"./node_modules/core-js/internals/to-integer-or-infinity.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/math-trunc.js");A.exports=function(A){var e=+A;return e!=e||0===e?0:n(e)}},"./node_modules/core-js/internals/to-length.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/to-integer-or-infinity.js"),r=Math.min;A.exports=function(A){var e=n(A);return e>0?r(e,9007199254740991):0}},"./node_modules/core-js/internals/to-object.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/require-object-coercible.js"),r=Object;A.exports=function(A){return r(n(A))}},"./node_modules/core-js/internals/to-primitive.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-call.js"),r=t("./node_modules/core-js/internals/is-object.js"),o=t("./node_modules/core-js/internals/is-symbol.js"),i=t("./node_modules/core-js/internals/get-method.js"),s=t("./node_modules/core-js/internals/ordinary-to-primitive.js"),a=t("./node_modules/core-js/internals/well-known-symbol.js"),c=TypeError,u=a("toPrimitive");A.exports=function(A,e){if(!r(A)||o(A))return A;var t,a=i(A,u);if(a){if(void 0===e&&(e="default"),t=n(a,A,e),!r(t)||o(t))return t;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(A,e)}},"./node_modules/core-js/internals/to-property-key.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/to-primitive.js"),r=t("./node_modules/core-js/internals/is-symbol.js");A.exports=function(A){var e=n(A,"string");return r(e)?e:e+""}},"./node_modules/core-js/internals/to-string-tag-support.js":function(A,e,t){"use strict";var n={};n[t("./node_modules/core-js/internals/well-known-symbol.js")("toStringTag")]="z",A.exports="[object z]"===String(n)},"./node_modules/core-js/internals/to-string.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/classof.js"),r=String;A.exports=function(A){if("Symbol"===n(A))throw new TypeError("Cannot convert a Symbol value to a string");return r(A)}},"./node_modules/core-js/internals/try-to-string.js":function(A){"use strict";var e=String;A.exports=function(A){try{return e(A)}catch(A){return"Object"}}},"./node_modules/core-js/internals/uid.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/function-uncurry-this.js"),r=0,o=Math.random(),i=n(1..toString);A.exports=function(A){return"Symbol("+(void 0===A?"":A)+")_"+i(++r+o,36)}},"./node_modules/core-js/internals/use-symbol-as-uid.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/symbol-constructor-detection.js");A.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},"./node_modules/core-js/internals/v8-prototype-define-bug.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/descriptors.js"),r=t("./node_modules/core-js/internals/fails.js");A.exports=n&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},"./node_modules/core-js/internals/weak-map-basic-detection.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=t("./node_modules/core-js/internals/is-callable.js"),o=n.WeakMap;A.exports=r(o)&&/native code/.test(String(o))},"./node_modules/core-js/internals/well-known-symbol.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/global-this.js"),r=t("./node_modules/core-js/internals/shared.js"),o=t("./node_modules/core-js/internals/has-own-property.js"),i=t("./node_modules/core-js/internals/uid.js"),s=t("./node_modules/core-js/internals/symbol-constructor-detection.js"),a=t("./node_modules/core-js/internals/use-symbol-as-uid.js"),c=n.Symbol,u=r("wks"),l=a?c.for||c:c&&c.withoutSetter||i;A.exports=function(A){return o(u,A)||(u[A]=s&&o(c,A)?c[A]:l("Symbol."+A)),u[A]}},"./node_modules/core-js/modules/es.array.filter.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/array-iteration.js").filter;n({target:"Array",proto:!0,forced:!t("./node_modules/core-js/internals/array-method-has-species-support.js")("filter")},{filter:function(A){return r(this,A,arguments.length>1?arguments[1]:void 0)}})},"./node_modules/core-js/modules/es.array.find.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/array-iteration.js").find,o=t("./node_modules/core-js/internals/add-to-unscopables.js"),i="find",s=!0;i in[]&&Array(1)[i]((function(){s=!1})),n({target:"Array",proto:!0,forced:s},{find:function(A){return r(this,A,arguments.length>1?arguments[1]:void 0)}}),o(i)},"./node_modules/core-js/modules/es.array.includes.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/array-includes.js").includes,o=t("./node_modules/core-js/internals/fails.js"),i=t("./node_modules/core-js/internals/add-to-unscopables.js");n({target:"Array",proto:!0,forced:o((function(){return!Array(1).includes()}))},{includes:function(A){return r(this,A,arguments.length>1?arguments[1]:void 0)}}),i("includes")},"./node_modules/core-js/modules/es.object.assign.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/object-assign.js");n({target:"Object",stat:!0,arity:2,forced:Object.assign!==r},{assign:r})},"./node_modules/core-js/modules/es.string.includes.js":function(A,e,t){"use strict";var n=t("./node_modules/core-js/internals/export.js"),r=t("./node_modules/core-js/internals/function-uncurry-this.js"),o=t("./node_modules/core-js/internals/not-a-regexp.js"),i=t("./node_modules/core-js/internals/require-object-coercible.js"),s=t("./node_modules/core-js/internals/to-string.js"),a=t("./node_modules/core-js/internals/correct-is-regexp-logic.js"),c=r("".indexOf);n({target:"String",proto:!0,forced:!a("includes")},{includes:function(A){return!!~c(s(i(this)),s(o(A)),arguments.length>1?arguments[1]:void 0)}})},"./node_modules/flatted/esm/index.js":function(A,e,t){"use strict";t.r(e),t.d(e,{fromJSON:function(){return w},parse:function(){return f},stringify:function(){return p},toJSON:function(){return h}});const{parse:n,stringify:r}=JSON,{keys:o}=Object,i=String,s="string",a={},c="object",u=(A,e)=>e,l=A=>A instanceof i?i(A):A,d=(A,e)=>typeof e===s?new i(e):e,B=(A,e,t,n)=>{const r=[];for(let s=o(t),{length:u}=s,l=0;l<u;l++){const o=s[l],u=t[o];if(u instanceof i){const i=A[u];typeof i!==c||e.has(i)?t[o]=n.call(t,o,i):(e.add(i),t[o]=a,r.push({k:o,a:[A,e,i,n]}))}else t[o]!==a&&(t[o]=n.call(t,o,u))}for(let{length:A}=r,e=0;e<A;e++){const{k:A,a:o}=r[e];t[A]=n.call(t,A,B.apply(null,o))}return t},g=(A,e,t)=>{const n=i(e.push(t)-1);return A.set(t,n),n},f=(A,e)=>{const t=n(A,d).map(l),r=t[0],o=e||u,i=typeof r===c&&r?B(t,new Set,r,o):r;return o.call({"":i},"",i)},p=(A,e,t)=>{const n=e&&typeof e===c?(A,t)=>""===A||-1<e.indexOf(A)?t:void 0:e||u,o=new Map,i=[],a=[];let l=+g(o,i,n.call({"":A},"",A)),d=!l;for(;l<i.length;)d=!0,a[l]=r(i[l++],B,t);return"["+a.join(",")+"]";function B(A,e){if(d)return d=!d,e;const t=n.call(this,A,e);switch(typeof t){case c:if(null===t)return t;case s:return o.get(t)||g(o,i,t)}return t}},h=A=>n(p(A)),w=A=>f(r(A))},"./node_modules/uuid/dist/esm-browser/native.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return n}});const n={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)}},"./node_modules/uuid/dist/esm-browser/regex.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return n}});const n=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i},"./node_modules/uuid/dist/esm-browser/rng.js":function(A,e,t){"use strict";let n;t.r(e),t.d(e,{default:function(){return o}});const r=new Uint8Array(16);function o(){if(!n&&(n="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!n))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return n(r)}},"./node_modules/uuid/dist/esm-browser/stringify.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return i},unsafeStringify:function(){return o}});var n=t("./node_modules/uuid/dist/esm-browser/validate.js");const r=[];for(let A=0;A<256;++A)r.push((A+256).toString(16).slice(1));function o(A,e=0){return r[A[e+0]]+r[A[e+1]]+r[A[e+2]]+r[A[e+3]]+"-"+r[A[e+4]]+r[A[e+5]]+"-"+r[A[e+6]]+r[A[e+7]]+"-"+r[A[e+8]]+r[A[e+9]]+"-"+r[A[e+10]]+r[A[e+11]]+r[A[e+12]]+r[A[e+13]]+r[A[e+14]]+r[A[e+15]]}const i=function(A,e=0){const t=o(A,e);if(!(0,n.default)(t))throw TypeError("Stringified UUID is invalid");return t}},"./node_modules/uuid/dist/esm-browser/v4.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return i}});var n=t("./node_modules/uuid/dist/esm-browser/native.js"),r=t("./node_modules/uuid/dist/esm-browser/rng.js"),o=t("./node_modules/uuid/dist/esm-browser/stringify.js");const i=function(A,e,t){if(n.default.randomUUID&&!e&&!A)return n.default.randomUUID();const i=(A=A||{}).random||(A.rng||r.default)();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,e){t=t||0;for(let A=0;A<16;++A)e[t+A]=i[A];return e}return(0,o.unsafeStringify)(i)}},"./node_modules/uuid/dist/esm-browser/validate.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return r}});var n=t("./node_modules/uuid/dist/esm-browser/regex.js");const r=function(A){return"string"==typeof A&&n.default.test(A)}},"./src/ABTest.js":function(A,e,t){"use strict";t.r(e),t.d(e,{fetchABTest:function(){return a}});var n=t("./src/util.js"),r=t("./src/config.js");function o(A){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},o(A)}function i(){i=function(){return e};var A,e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},s=r.iterator||"@@iterator",a=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function u(A,e,t,n){return Object.defineProperty(A,e,{value:t,enumerable:!n,configurable:!n,writable:!n})}try{u({},"")}catch(A){u=function(A,e,t){return A[e]=t}}function l(e,t,n,r){var o=t&&t.prototype instanceof g?t:g,i=Object.create(o.prototype);return u(i,"_invoke",function(e,t,n){var r=1;return function(o,i){if(3===r)throw Error("Generator is already running");if(4===r){if("throw"===o)throw i;return{value:A,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var a=v(s,n);if(a){if(a===B)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(1===r)throw r=4,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=3;var c=d(e,t,n);if("normal"===c.type){if(r=n.done?4:2,c.arg===B)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=4,n.method="throw",n.arg=c.arg)}}}(e,n,new I(r||[])),!0),i}function d(A,e,t){try{return{type:"normal",arg:A.call(e,t)}}catch(A){return{type:"throw",arg:A}}}e.wrap=l;var B={};function g(){}function f(){}function p(){}var h={};u(h,s,(function(){return this}));var w=Object.getPrototypeOf,C=w&&w(w(b([])));C&&C!==t&&n.call(C,s)&&(h=C);var Q=p.prototype=g.prototype=Object.create(h);function m(A){["next","throw","return"].forEach((function(e){u(A,e,(function(A){return this._invoke(e,A)}))}))}function U(A,e){function t(r,i,s,a){var c=d(A[r],A,i);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==o(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(A){t("next",A,s,a)}),(function(A){t("throw",A,s,a)})):e.resolve(l).then((function(A){u.value=A,s(u)}),(function(A){return t("throw",A,s,a)}))}a(c.arg)}var r;u(this,"_invoke",(function(A,n){function o(){return new e((function(e,r){t(A,n,e,r)}))}return r=r?r.then(o,o):o()}),!0)}function v(e,t){var n=t.method,r=e.i[n];if(r===A)return t.delegate=null,"throw"===n&&e.i.return&&(t.method="return",t.arg=A,v(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),B;var o=d(r,e.i,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,B;var i=o.arg;return i?i.done?(t[e.r]=i.value,t.next=e.n,"return"!==t.method&&(t.method="next",t.arg=A),t.delegate=null,B):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,B)}function y(A){this.tryEntries.push(A)}function F(e){var t=e[4]||{};t.type="normal",t.arg=A,e[4]=t}function I(A){this.tryEntries=[[-1]],A.forEach(y,this),this.reset(!0)}function b(e){if(null!=e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=A,t.done=!0,t};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return f.prototype=p,u(Q,"constructor",p),u(p,"constructor",f),f.displayName=u(p,c,"GeneratorFunction"),e.isGeneratorFunction=function(A){var e="function"==typeof A&&A.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,p):(A.__proto__=p,u(A,c,"GeneratorFunction")),A.prototype=Object.create(Q),A},e.awrap=function(A){return{__await:A}},m(U.prototype),u(U.prototype,a,(function(){return this})),e.AsyncIterator=U,e.async=function(A,t,n,r,o){void 0===o&&(o=Promise);var i=new U(l(A,t,n,r),o);return e.isGeneratorFunction(t)?i:i.next().then((function(A){return A.done?A.value:i.next()}))},m(Q),u(Q,c,"Generator"),u(Q,s,(function(){return this})),u(Q,"toString",(function(){return"[object Generator]"})),e.keys=function(A){var e=Object(A),t=[];for(var n in e)t.unshift(n);return function A(){for(;t.length;)if((n=t.pop())in e)return A.value=n,A.done=!1,A;return A.done=!0,A}},e.values=b,I.prototype={constructor:I,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(F),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=A)},stop:function(){this.done=!0;var A=this.tryEntries[0][4];if("throw"===A.type)throw A.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(A){i.type="throw",i.arg=e,t.next=A}for(var r=t.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],i=o[4],s=this.prev,a=o[1],c=o[2];if(-1===o[0])return n("end"),!1;if(!a&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=s){if(s<a)return this.method="next",this.arg=A,n(a),!0;if(s<c)return n(c),!1}}},abrupt:function(A,e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[0]>-1&&n[0]<=this.prev&&this.prev<n[2]){var r=n;break}}r&&("break"===A||"continue"===A)&&r[0]<=e&&e<=r[2]&&(r=null);var o=r?r[4]:{};return o.type=A,o.arg=e,r?(this.method="next",this.next=r[2],B):this.complete(o)},complete:function(A,e){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&e&&(this.next=e),B},finish:function(A){for(var e=this.tryEntries.length-1;e>=0;--e){var t=this.tryEntries[e];if(t[2]===A)return this.complete(t[4],t[3]),F(t),B}},catch:function(A){for(var e=this.tryEntries.length-1;e>=0;--e){var t=this.tryEntries[e];if(t[0]===A){var n=t[4];if("throw"===n.type){var r=n.arg;F(t)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={i:b(e),r:t,n},"next"===this.method&&(this.arg=A),B}},e}function s(A,e,t,n,r,o,i){try{var s=A[o](i),a=s.value}catch(A){return void t(A)}s.done?e(a):Promise.resolve(a).then(n,r)}var a=function(){var A,e=(A=i().mark((function A(e){var t,o,s,a,c,u,l,d,B,g;return i().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(t=e.userId,o=e.id,s=e.callback,a=void 0===s?null:s,t&&o){A.next=3;break}return A.abrupt("return");case 3:return c={id:o,appId:n.UTIL.getAppId(),userId:t},A.next=6,n.UTIL.httpRequest("".concat(r.CONFIG.GLOBAL_VARS.domain,"/abtest/traffic/result"),"post",c);case 6:u=A.sent,l=(null==u?void 0:u.result)||{},d=l.group,B=l.abId,g=l.abStatus,l.experimentId===o&&d&&(n.UTIL.setUid(t),r.CONFIG.GLOBAL_VARS.ABtestInfo={bucket:d,abId:B,abStatus:g}),a&&a(u);case 10:case"end":return A.stop()}}),A)})),function(){var e=this,t=arguments;return new Promise((function(n,r){var o=A.apply(e,t);function i(A){s(o,n,r,i,a,"next",A)}function a(A){s(o,n,r,i,a,"throw",A)}i(void 0)}))});return function(A){return e.apply(this,arguments)}}()},"./src/apiUtil.js":function(A,e,t){"use strict";t.r(e),t.d(e,{getApiPerformance:function(){return r},getTraceId:function(){return o}});var n=t("./src/util.js");function r(A){var e={fs:0,dr:0,stt:"",protocol:"",transfer_size:0};try{var t=window.performance.getEntriesByType("resource").filter((function(e){return e.name===A})),r=t.length?t[t.length-1]:null;if(r){var o=r.startTime,i=r.fetchStart,s=r.connectEnd,a=r.responseEnd,c=r.responseStart,u=r.requestStart,l=r.connectStart,d=r.duration,B=r.nextHopProtocol,g=r.transferSize,f=void 0===g?0:g,p=n.UTIL.getPositiveInt(d),h=n.UTIL.getPositiveInt(i-o)+n.UTIL.getPositiveInt(l-i)+n.UTIL.getPositiveInt(u-s),w=n.UTIL.getPositiveInt(s-l),C=n.UTIL.getPositiveInt(c-(u||i)),Q=c?n.UTIL.getPositiveInt(a-c):p-h-w-C;e={fs:n.UTIL.getPositiveInt(i),dr:p,stt:"".concat(h,",").concat(w,",").concat(C,",").concat(Q),protocol:B,transfer_size:f}}}catch(A){n.UTIL.setFrVar({apiUtilErr:!0})}return e}function o(A){for(var e=0,t=[{name:"cf2-x-hw-request-trace-id",prefix:""},{name:"x-request-id",prefix:""},{name:"furion_traceid",prefix:""},{name:"lubanops-ntrace-id",prefix:"v2-"},{name:"wise_traceid",prefix:"v1-"}];e<t.length;e++){var n=t[e],r=n.name,o=n.prefix,i=A.get(r);if(i)return"".concat(o).concat(i)}return null}},"./src/check-upload.js":function(A,e,t){"use strict";t.r(e),t.d(e,{checkAfterLoad:function(){return c}});t("./src/heat-new.js");var n=t("./src/style.js"),r=t("./src/util.js"),o=t("./src/config.js"),i=function(A,e){var t;r.UTIL.needReport()&&(null===(t=r.UTIL.httpRequest(A,"get",{}))||void 0===t||t.then((function(A){e(A.result)})).catch((function(A){r.UTIL.setFrVar({checkError:!0})})))},s=function(A){var e=r.UTIL.getAppId(),t=o.CONFIG.GLOBAL_VARS.domain,s="".concat(t,"/checkStyle?appId=").concat(e,"&url=").concat(A);i(s,(function(A){var e=A.url,t=A.id,r=A.clsName,o=A.extId,i=A.extCls;e&&!function(A,e){var t,n,r=null==A||null===(t=A.split(","))||void 0===t?void 0:t.find((function(A){return document.getElementById(A)})),o=null==e||null===(n=e.split(","))||void 0===n?void 0:n.find((function(A){var e;return null===(e=document.getElementsByClassName(A))||void 0===e?void 0:e.length}));return r||o}(o,i)&&(0,n.uploadPageStyle)(e,t,r)}))},a=function(A){var e,t=!o.CONFIG.VISIT_INFO.browser.includes("IE"),n=1===window.devicePixelRatio;return A&&t&&n&&(e=document.documentElement.outerHTML,!["data-darkreader",'nighteye="active"',"api-interceptor","saladict-dictpanel-root","saladict-saladbowl-root","window_resizer_tooltip_wrapper","window-resizer-tooltip","wt_ide_left_arrow","xl_chrome_ext"].find((function(A){return e.includes(A)})))&&function(){var A=!0,e="fr-test-font",t="12px";document.body.appendChild(document.createElement(e));var n=document.getElementsByTagName(e)[0];return n&&(n.style.fontSize=t,A=getComputedStyle(n).fontSize===t),document.body.removeChild(n),A}()},c=function(){window.addEventListener("load",(function(){setTimeout((function(){var A=r.UTIL.getReportUrl();a(A)&&s(A),function(A,e){var t=r.UTIL.getAppId(),s=o.CONFIG.GLOBAL_VARS.domain,a="".concat(s,"/check?appId=").concat(t,"&url=").concat(A,"&types=").concat(e.join(","));i(a,(function(e){var t=e.version,r=e.cc;t&&(0,n.uploadVersion)(A),r&&(o.CONFIG.GLOBAL_VARS.checkComponentConfig=!0)}))}(A,["version","cc"])}),3e3)}),!0)}},"./src/codeLess.js":function(A,e,t){"use strict";t.r(e),t.d(e,{Codeless:function(){return lA}});var n,r=t("./src/util.js"),o=t("./src/newToast.js"),i=t("./src/service.js"),s=t("./src/newNav.js"),a=t("./src/newPop.js"),c=t("./src/domActions.js"),u=t("./src/config.js"),l=t("./src/codeLessConfig.js");function d(A){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},d(A)}function B(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function g(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?B(Object(t),!0).forEach((function(e){C(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):B(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function f(A){return function(A){if(Array.isArray(A))return w(A)}(A)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(A)||h(A)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(A,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(A,Q(n.key),n)}}function h(A,e){if(A){if("string"==typeof A)return w(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?w(A,e):void 0}}function w(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function C(A,e,t){return(e=Q(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function Q(A){var e=function(A,e){if("object"!=d(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var n=t.call(A,e||"default");if("object"!=d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==d(e)?e:e+""}var m=[t("./src/images/helping.svg"),t("./src/images/filter.svg")],U=[],v="",y=null,F={0:"默认匹配",1:"模糊匹配",2:"正则匹配"},I=0,b=1,E=2,L="xpath",x="id",H="text",S={0:"xpath",1:"id",2:"text"},T="xpathPointMatch",O="idPointMatch",M="tcPointMatch",D="furion-point-txt",j="元素full xpath",N="组件文案",K="元素id",_="eventCommon",k="eventUnique",G="equal",P="regularMatch",R="fx",V="eid",z="tc",X=C(C(C({},I,{describe:"聚合URL",name:"formated_url",operator:"aggRule",showName:"聚合URL"}),b,{describe:"用户上报的原始URL,包含参数",name:"url",operator:"customContain",showName:"页面地址"}),E,{describe:"用户上报的原始URL,包含参数",name:"url",operator:"regularMatch",showName:"页面地址"}),J=[];function W(A){var e;J=[];var t=y.querySelector("input[type=radio][name=matchPointType]:checked").value,n=y.querySelector("#chosenThisUrl").checked,r=null===(e=y.querySelector("input[type=radio][name=matchPageType]:checked"))||void 0===e?void 0:e.value,i=y.querySelector("#onlyMatchText").checked,s=A.id;if(t===l.CODELESS_CONFIG.noPointEventTypeMap.xpath)if(U.includes(1)){var a=function(A,e){for(var t=/\[[^\]]*\]/g,n="\\[?[0-9]*\\]?",r=e.split("/"),o=0;o<A.length;o++)A[o]>0?r[o+1]=r[o+1].replace(t,n):r[o+1]=r[o+1].replace(/[\[\]]/g,"\\$&");return"".concat(r.join("/"),".*")}(U,v),u=Z(j,R,P,k,[a]);J.push(u)}else{var d=Z(j,R,G,k,[v]);J.push(d)}else if(t===l.CODELESS_CONFIG.noPointEventTypeMap.id){if(!s)return void o.TOAST.toast("所选元素没有id",3e3);var B=Z(K,V,G,k,[s]);J.push(B)}else if(t===l.CODELESS_CONFIG.noPointEventTypeMap.tc&&!l.CODELESS_CONFIG.DOMS_TEXT_LIST.length)return void o.TOAST.toast("所选元素没有文案",3e3);!function(A,e,t){if(A){var n=y.querySelector(".match-page-rule-ipt").value,r=e===I?location.href:n||location.href,o=X[n?e:I],i=Z(o.describe,o.name,o.operator,_,[r],o.showName);J.push(i)}if(t){var s=Z(N,z,G,k,l.CODELESS_CONFIG.DOMS_TEXT_LIST.slice(0,5));J.push(s)}}(n,r,i),function(){var A=c.DOMACTIONS.createNode("div","furion-confirm-container"),e=c.DOMACTIONS.createNode("div","custom-confirm-box-overlay","overlay");A.appendChild(e);var t=c.DOMACTIONS.createNode("div","custom-confirm-box","confirmBox"),n=c.DOMACTIONS.createNode("p","custom-confirm-title","","是否为绿区"),r=c.DOMACTIONS.createNode("button","","","是");r.onclick=Y.bind(this,!0);var o=c.DOMACTIONS.createNode("button","","","否");o.onclick=Y.bind(this,!1),t.appendChild(n),t.appendChild(r),t.appendChild(o),A.appendChild(t),y.appendChild(A)}()}function Y(A){var e=r.UTIL.getConfig(),t=e.appId,n=e.multFrUrl,o=e.yMultFrUrl,i=new URL(A?n:o);i.searchParams.append("appId",t),i.searchParams.append("expressions",encodeURIComponent(JSON.stringify(J))),window.open(i),y.querySelector(".furion-confirm-container").remove()}function Z(A,e,t,n,r,o){return{logic:"OR",conditions:[{dataType:"String",describe:A,name:e,operator:t,showName:o||A,type:n,valueList:r}]}}function q(A){A.stopPropagation()}function $(){var A=(y=document.querySelector(".code-less-box").shadowRoot).querySelector(".furion-submit"),e=y.querySelector(".furion-query"),t=y.querySelector(".furion-popClose"),s=y.querySelector(".furion-codeBox");y.getElementById("pop-furion").addEventListener("click",q,!1),A.onclick=function(){if(y.querySelector("#target_name").value)!function(A){var e,t,s=r.UTIL.getConfig(),a=s.searchDomain,u=s.region,d=y.querySelector("#onlyMatchText").checked?l.CODELESS_CONFIG.DOMS_TEXT_LIST:[],B=(y.querySelector("#onlyMatchClass").checked&&n.getAttribute("class")&&y.querySelector(".furion-match-class-content").innerText,(null===(e=y.querySelector("input[type=radio][name=matchPageType]:checked"))||void 0===e?void 0:e.value)||""),g=y.querySelector("input[type=radio][name=matchPointType]:checked").value,f=y.querySelector(".match-page-rule-ipt").value;if(g!==l.CODELESS_CONFIG.noPointEventTypeMap.id||A.id)if(g!==l.CODELESS_CONFIG.noPointEventTypeMap.tc||l.CODELESS_CONFIG.DOMS_TEXT_LIST.length){var p={appId:r.UTIL.getAppId(),region:u,url:r.UTIL.getReportUrl(),eventName:y.querySelector("#target_name").value,xpath:g===l.CODELESS_CONFIG.noPointEventTypeMap.xpath?r.UTIL.getCssSelector(A):void 0,fx:g===l.CODELESS_CONFIG.noPointEventTypeMap.xpath?v:void 0,userId:r.UTIL.getUserId(),fuzzys:g===l.CODELESS_CONFIG.noPointEventTypeMap.xpath?U:void 0,matchPage:y.querySelector("#chosenThisUrl").checked,tcList:d,noPointEventType:g,eid:g===l.CODELESS_CONFIG.noPointEventTypeMap.id?A.id:void 0,ecls:"",matchPageType:B,matchPageUrl:f};null===(t=r.UTIL.httpRequest("".concat(a,"/nopoint/addNoPointEvent"),"post",p))||void 0===t||t.then((function(A){var e,t;"success"===(null==A?void 0:A.status)?(y.querySelector("#pop-furion").style.display="none",null===(e=y.querySelector("#eventFilter"))||void 0===e||e.setAttribute("src",m[1]),c.DOMACTIONS.removeActive("active"),o.TOAST.toast("提交成功",3e3),y.querySelector(".switch").checked&&i.API.getEventList().then((function(A){c.DOMACTIONS.showAllMarkedDom(A||[])}))):o.TOAST.toast(null==A||null===(t=A.result)||void 0===t?void 0:t.message,3e3)})).catch((function(A){o.TOAST.toast("提交失败",3e3)}))}else o.TOAST.toast("所选元素没有文案",3e3);else o.TOAST.toast("所选元素没有id",3e3)}(n);else{var A=y.querySelector("#name_is_empty");A.style.animation="show 1.5s",A.style.display="block"}},e.onclick=function(){W(n)},t.onclick=function(){y.getElementById("pop-furion").style.display="none",window.removeEventListener("focus",q,!0)},u.CONFIG.VISIT_INFO.browser.includes("IE")?s.onpropertychange=function(A){AA(A)}:s.oninput=function(A){AA(A)}}function AA(A){if("target_name"===A.target.id){var e=y.querySelector("#name_is_empty");e.style.animation="hide 1.5s",e.style.display="none"}}function eA(){y.querySelector("#text-list-box").innerHTML="";for(var A=function(A){var e=c.DOMACTIONS.createNode("div","text-content-item"),t=c.DOMACTIONS.createNode("div","text-content-text","text-content-text-".concat(A),"".concat(l.CODELESS_CONFIG.DOMS_TEXT_LIST[A]));t.setAttribute("title",l.CODELESS_CONFIG.DOMS_TEXT_LIST[A]);var n=c.DOMACTIONS.createNode("div","delete-text","delete-text-id-".concat(A),"X");n.addEventListener("click",(function(){var n,r=l.CODELESS_CONFIG.DOMS_TEXT_LIST.indexOf(y.querySelector("#text-content-text-".concat(A)).innerText);l.CODELESS_CONFIG.DOMS_TEXT_LIST.splice(r,1),e.remove();for(var o=y.querySelectorAll(".function-dom-active"),i=null===(n=Array.from(o))||void 0===n?void 0:n.filter((function(A){var e;return A.getAttribute("dom_tc")===(null===(e=t.innerText)||void 0===e?void 0:e.trim())})),s=i.length-1;s>=0;s--)i[s].remove()})),e.appendChild(t),e.appendChild(n),y.querySelector("#text-list-box").appendChild(e)},e=0;e<l.CODELESS_CONFIG.DOMS_TEXT_LIST.length;e++)A(e)}var tA=function(){return A=function A(e,t,n){!function(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}(this,A),this.element=c.DOMACTIONS.createNode.apply(c.DOMACTIONS,[e].concat(f(t))),this.setAttributes(n)},(e=[{key:"setAttributes",value:function(A){var e=this;Object.keys(A).forEach((function(t){e.element.setAttribute(t,A[t])}))}},{key:"appendChildEle",value:function(A){this.element.appendChild(A.getElement())}},{key:"appendChild",value:function(A){this.element.appendChild(A)}},{key:"getElement",value:function(){return this.element}}])&&p(A.prototype,e),t&&p(A,t),Object.defineProperty(A,"prototype",{writable:!1}),A;var A,e,t}();function nA(A,e){e.style.display="none",y.querySelector("#furion-point-fx-content").remove();var t=iA(r.UTIL.travelWithFunc(A,r.UTIL.fxFun));e.appendChild(t),U=sA(r.UTIL.travelWithFunc(A,r.UTIL.fxFun).split("/")),c.DOMACTIONS.getPeersDom(r.UTIL.travelWithFunc(A,r.UTIL.fxFun),-1,"active"),y.querySelector("#furion-point-num").innerHTML=y.querySelectorAll(".function-dom-active").length,eA()}function rA(A,e){var t=function(A){var e=new tA("div",["furion-codeBox-item furion-point-match-type-div"],{}),t=new tA("div",[D,"","元素匹配类型"],{});t.appendChild(A.matchPointType);var n=new tA("span",["","","："],{});return t.appendChildEle(n),e.appendChildEle(t),e.getElement()}(e);t.onchange=function(e){var t=y.querySelector("#furion-point-fx-box");e.target.value===l.CODELESS_CONFIG.noPointEventTypeMap.xpath?(t.style.display="block",y.querySelector("#furion-point-id-box").style.display="none",y.querySelector("#onlyMatchText").disabled=!1):e.target.value===l.CODELESS_CONFIG.noPointEventTypeMap.id?(nA(A,t),y.querySelector("#furion-point-id-box").style.display="block",y.querySelector("#onlyMatchText").disabled=!1):(nA(A,t),y.querySelector("#chosenThisUrl").checked=!0,y.querySelector(".furion-match-type-div").style.display="flex",y.querySelector(".furion-match-rule-div").style.display="flex",y.querySelector("#furion-point-id-box").style.display="none",y.querySelector("#onlyMatchText").checked=!0,y.querySelector("#onlyMatchText").disabled=!0)};var n={type:"radio",name:"matchPointType",value:0},r=oA(new tA("input",["",T],g(g({},n),{},{checked:!0})).getElement(),L,T),o=oA(new tA("input",["",O],g(g({},n),{},{value:1})).getElement(),x,O),i=oA(new tA("input",["",M],g(g({},n),{},{value:2})).getElement(),H,M);return t.appendChild(r),t.appendChild(o),t.appendChild(i),t}function oA(A,e,t){var n=new tA("label",[D,"",e],{for:t}),r=new tA("div",["",""],{});return r.appendChild(A),r.appendChildEle(n),r.getElement()}function iA(A){if(!A)return null;for(var e=c.DOMACTIONS.createNode("span","furion-point-txt","furion-point-fx-content"),t=A.split("/"),n=1;n<t.length;n++){var r=t[n].indexOf("[")>-1?"furion-fx-click":"furion-fx-item",o=c.DOMACTIONS.createNode("a",r,"",t[n]);if(o.setAttribute("index",n),n>1){var i=c.DOMACTIONS.createNode("span","furion-fx-sever","",">");e.appendChild(i)}e.appendChild(o)}U=sA(t);var s=[];return e.onclick=function(e){var t=e.target;if("A"===t.nodeName&&-1!==t.innerHTML.indexOf("[")){var n=t.getAttribute("index");t.classList.contains("furion-fx-item-active")?(t.classList.remove("furion-fx-item-active"),s=s.filter((function(A){return A!==n-1})),U[n-1]=0):(t.classList.add("furion-fx-item-active"),s.push(n-1),s.sort((function(A,e){return A-e})),U[n-1]=1),c.DOMACTIONS.getPeersDom(A,s.length?s:-1,"active"),y.getElementById("furion-point-num").innerHTML=y.querySelectorAll(".function-dom-active").length,y.querySelector("#match-text-box").style.display=l.CODELESS_CONFIG.DOMS_TEXT_LIST.length?"flex":"none",eA(),y.querySelector("#match-class-box").style.display="none",y.querySelector(".furion-match-class-content").innerHTML=""}},e}function sA(A){return A.filter((function(A){return""!==A})).map((function(A){return 0}))}function aA(){for(var A=document.getElementsByTagName("iframe"),e=function(e){setTimeout((function(){A[e].contentWindow.postMessage("initCodeless","*")}),500)},t=0;t<A.length;t++)e(t)}function cA(){var A=c.DOMACTIONS.createNode("div","code-less-box");A.attachShadow({mode:"open"}),document.body.appendChild(A),s.NEWNAV.createNav(),a.NEWPOP.createPop(),o.TOAST.newToast(),s.NEWNAV.onloadNavEvent(),$(),c.DOMACTIONS.addCodeLessCSS('.function-dom-active,.function-dom-hover{position:fixed;top:0;left:0;border:#5e7ce0 solid 2px;background:transparent;pointer-events: none;z-index:9999;}.function-dom-hover{box-shadow:0px 0px 10px #6cbfff;border-color:#6cbfff;}.function-dom-active{background-color:rgba(171,171,227,0.3);}\n         .furion-codeBox{padding:30px 20px 60px;}.furion-codeBox-item{margin-bottom:10px;margin-top:16px;width:360px;word-break: break-all;}.furion-point-star{display:inline-block;vertical-align:middle;color:#f33e21;margin-right:5px;}#name_is_empty{display:none;color:#f33e21;font-size:14px;padding-left:80px;margin-top:10px;}.furion-point-txt{font-size:14px;color:#333;cursor: pointer;}.furion-point-value{outline-color:#4e5be9;border:1px solid #adb0b8;border-radius:2px;padding:4px 8px;width:210px;}.furion-fx-item,.furion-fx-sever,a.furion-fx-item:hover{color:#999;}.furion-fx-sever{margin:0 5px;}.furion-fx-click{cursor:pointer;color:#526ecc;}.furion-fx-click:hover,.furion-fx-item-active{color:#f33e21;}.pop-checkbox{margin-left:0;vertical-align:middle;}.furion-comment{margin-left:2px;width:12px;}\n         .furion-popover {display: inline-block; position:relative;}.furion-popover-content { z-index: 100000;margin-top: 5px;position: absolute; width: 200px; padding: 10px; background: #464d6e;border-radius: 4px; color: #dfe1e6; font-size: 14px; left: -84px; box-shadow: 0 4px 16px #252b3a3d; display: none;}.furion-popover-content:before {width: 10px; height: 10px;display: block;content: ""; background: #464d6e;position: absolute; top: -5px; left: 100px; transform: rotate(45deg);}.furion-popover-item:hover + .furion-popover-content {display: block;}\n         .furion-match-text-content,.furion-match-class-content {width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:14px;color:#526ecc;cursor:default}\n         .text-content-box {display: flex;justify-content: space-between;flex-wrap: wrap;box-sizing: border-box;font-size: 14px;}.text-content-item {border: 1px solid #ccc;border-radius: 4px;padding: 2px 4px;white-space: nowrap;display: flex;justify-content: space-between;align-items:center;box-sizing: border-box;width:45%;cursor:pointer;margin-top:8px;background-color:#eef0f5;}.text-content-text {padding: 0;max-width: 125px;overflow: hidden; text-overflow: ellipsis; white-space: nowrap;box-sizing: border-box;color:#526ecc;}.delete-text {cursor: pointer;width: 16px;height: 16px;background: #d5d5db;border-radius: 50%;color: #fff;text-align: center;line-height: 16px;font-size:8px;}.delete-text:hover {color: #526ecc;}\n         .furion-match-type-div,.furion-point-match-type-div {display: flex;justify-content:flex-start;}.reg-match-div {margin-left:4px}.match-page-rule-ipt {outline-color:#4e5be9;border:1px solid #adb0b8;border-radius:2px;padding:4px 8px;width:210px;}\n         .function-dom-marked{position:fixed;top:0;left:0;border:#ff0000 solid 2px;background:transparent;pointer-events: none;z-index:9999;}\n         ')}function uA(){aA(),cA()}var lA={init:function(){(window.__fr||{}).showCodeless=uA,window.addEventListener("message",(function(A){"initCodeless"!==A.data||u.CONFIG.IS_IFRAME||(u.CONFIG.IS_IFRAME=!0,cA())})),r.UTIL.addLoadEvent((function(){var A,e="initCodeless"===window.name||"1"===r.UTIL.getQueryVariable("fr_codeless")||window.location.hash.includes("fr_codeless=1"),t="initHeatmap"===window.name||"1"===r.UTIL.getQueryVariable("fr_heatmap")||window.location.hash.includes("fr_heatmap=1"),n="initConsistency"===window.name||"1"===r.UTIL.getQueryVariable("fr_consistency")||window.location.hash.includes("fr_consistency=1");if((e||t||n)&&(aA(),cA()),t)null===(A=y.querySelector("#furion-heat-map"))||void 0===A||A.click();else if(n){var o;null===(o=y.querySelector("#furion-consistency"))||void 0===o||o.click()}}))},showShadow:function(A,e,t,o,i,s,a,u,d,B,f,p,h){if(window.addEventListener("focus",q,!0),n=A,"new"===t){var w=function(){var A={};return[{name:"pointName",comment:"埋点名称不能和已有事件名称重复"},{name:"pointElement",comment:"针对需要框选页面中某一类元素时，通常是列表的一行或者一列，\r可以尝试点击埋点元素中的蓝色字体进行模糊匹配并查看效果"},{name:"pointNumber",comment:"当前页面中能匹配到的元素个数"},{name:"matchThisUrl",comment:"对于页面中的公共组件，如菜单栏、公共头部等，建议不要勾选此选项，\r否则就只会匹配到当前URL路径下触发的事件上报数据，造成数据统计偏少。"},{name:"onlyMatchText",comment:"针对组件被复用，不同的文案，代表不同的业务逻辑，则需要勾选，默认不勾选。当匹配模式为tc模式时，此选项必选"},{name:"onlyMatchClass",comment:"针对组件被复用，不同的类属性，代表不用的业务逻辑，则需要勾选，默认不勾选。"},{name:"matchPageType",comment:"默认匹配：使用服务在Furion上设置的URL聚合规则进行匹配,\r模糊匹配：针对furion的聚合规则无法解决的场景，勾选此选项会通过like语法来进行url匹配,\r正则匹配：针对furion的聚合规则无法解决的场景，勾选此选项会通过正则表达式进行url匹配。"},{name:"matchPointType",comment:"页面dom元素存在id且id不会轻易变化时可使用id模式，dom元素的文案不会轻易变化时可使用tc模式,其余情况建议使用xpath模式"}].forEach((function(e){var t=c.DOMACTIONS.createNode("div","furion-popover");t.innerHTML='\n            <img class = "furion-comment furion-popover-item" src='.concat(m[0],'>\n            <div class="furion-popover-content">').concat(e.comment,"</div>\n        "),A[e.name]=t})),A}();U=sA(r.UTIL.travelWithFunc(A,r.UTIL.fxFun).split("/")),v=r.UTIL.travelWithFunc(A,r.UTIL.fxFun);var C=rA(A,w),Q=function(A){var e=new tA("div",["furion-codeBox-item",""],{}),t=new tA("input",["furion-point-value","target_name"],{value:""}),n=new tA("span",["furion-point-star","","*"],{}),r=new tA("span",[D,"","埋点名称"],{}),o=new tA("div",["","name_is_empty","请输入埋点名称"],{}),i=new tA("span",["","","："],{});return e.appendChildEle(n),e.appendChildEle(r),e.appendChild(A.pointName),e.appendChildEle(i),e.appendChildEle(t),e.appendChildEle(o),e.getElement()}(w),I=function(A,e){var t=new tA("div",["furion-codeBox-item","furion-point-fx-box"],{}),n=new tA("span",["","","："],{}),o=iA(r.UTIL.travelWithFunc(e,r.UTIL.fxFun)),i=new tA("span",[D,"","埋点元素"],{});return t.appendChildEle(i),t.appendChild(A.pointElement),t.appendChildEle(n),o&&t.appendChild(o),t.getElement()}(w,A),b=function(A){var e=new tA("div",["furion-codeBox-item","furion-point-id-box"],{}),t=new tA("span",[D,"",A.id||"--"],{}),n=new tA("span",[D,"","埋点id："],{});return e.appendChildEle(n),e.appendChildEle(t),e.getElement()}(A);b.style.display="none";var E=function(A){var e=new tA("div",["furion-codeBox-item"],{}),t=new tA("span",[D,"",A],{}),n=new tA("span",[D,"","埋点事件："],{});return e.appendChildEle(n),e.appendChildEle(t),e.getElement()}(e),L=function(A){var e=new tA("div",["furion-codeBox-item"],{}),t=new tA("span",[D,"furion-point-num",y.querySelectorAll(".function-dom-active").length],{}),n=new tA("span",[D,"","埋点元素个数"],{}),r=new tA("span",["","","："],{});return e.appendChildEle(n),e.appendChild(A.pointNumber),e.appendChildEle(r),e.appendChildEle(t),e.getElement()}(w),x=new tA("input",["pop-checkbox","chosenThisUrl"],{type:"checkbox"}).getElement(),H=function(A,e){var t=new tA("div",["furion-codeBox-item furion-checkbox-div"],{}),n=new tA("label",[D,"","只匹配当前页面"],{for:"chosenThisUrl"});return t.appendChild(e),t.appendChildEle(n),t.appendChild(A.matchThisUrl),t.getElement()}(w,x),T={type:"radio",name:"matchPageType",value:0},O=function(A){var e=new tA("div",["furion-codeBox-item furion-match-type-div"],{}),t=new tA("div",[D,"","页面匹配类型"],{});t.appendChild(A.matchPageType);var n=new tA("span",["","","："],{});return t.appendChildEle(n),e.appendChildEle(t),e.getElement()}(w),M=new tA("input",["","defaultMatch"],g(g({},T),{},{checked:!0})).getElement(),j=function(A){var e=new tA("label",[D,"","默认匹配"],{for:"defaultMatch"}),t=new tA("div",["",""],{});return t.appendChild(A),t.appendChildEle(e),t.getElement()}(M),N=new tA("input",["","fuzzyMatch"],g(g({},T),{},{value:1})).getElement(),K=function(A){var e=new tA("label",[D,"","模糊匹配"],{for:"fuzzyMatch"}),t=new tA("div",["",""],{});return t.appendChild(A),t.appendChildEle(e),t.getElement()}(N),_=new tA("input",["","regMatch"],g(g({},T),{},{value:2})).getElement(),k=function(A){var e=new tA("label",[D,"","正则匹配"],{for:"regMatch"}),t=new tA("div",["",""],{});return t.appendChild(A),t.appendChildEle(e),t.getElement()}(_);O.appendChild(j),O.appendChild(K),O.appendChild(k),O.style.display="none";var G=new tA("input",["match-page-rule-ipt","matchPageRuleInput"],{}).getElement(),P=function(A){var e=new tA("div",["furion-codeBox-item furion-match-rule-div"],{}),t=new tA("span",[D,"","页面匹配规则："],{});return e.appendChildEle(t),e.appendChild(A),e.getElement().style.display="none",e.getElement()}(G),R=function(A){var e=new tA("div",["furion-codeBox-item furion-checkbox-div","match-text-box"],{}),t=new tA("input",["pop-checkbox","onlyMatchText"],{type:"checkbox"}),n=new tA("label",[D,"","只匹配当前组件文案"],{for:"onlyMatchText"}),r=new tA("span",["","","："],{});return e.appendChildEle(t),e.appendChildEle(n),e.appendChild(A.onlyMatchText),e.appendChildEle(r),e.getElement()}(w),V=c.DOMACTIONS.createNode("div","text-content-box","text-list-box"),z=function(A,e){var t=new tA("div",["furion-codeBox-item furion-checkbox-div","match-class-box"],{}),n=new tA("input",["pop-checkbox","onlyMatchClass"],{type:"checkbox"}),r=new tA("label",[D,"","只匹配当前组件所属类"],{for:"onlyMatchClass"}),o=new tA("div",["furion-match-class-content","","".concat(e.getAttribute("class")||"")],{title:"".concat(e.getAttribute("class")||"")}),i=new tA("span",["","","："],{});return t.appendChildEle(n),t.appendChildEle(r),t.appendChild(A.onlyMatchClass),t.appendChildEle(i),t.appendChildEle(o),t.getElement()}(w,n),X=c.DOMACTIONS.createNode("div","furion-codeBox-point"),J=y.querySelector(".furion-codeBox");X.appendChild(C),X.appendChild(Q),X.appendChild(I),X.appendChild(b),X.appendChild(E),X.appendChild(L),X.appendChild(H),X.appendChild(O),X.appendChild(P),X.appendChild(R),X.appendChild(z),X.appendChild(V),x.onchange=function(){O.style.display=x.checked?"flex":"none",M.checked=!0,N.checked=!1,_.checked=!1,P.style.display=x.checked?"flex":"none",G.value=""},R.style.display=l.CODELESS_CONFIG.DOMS_TEXT_LIST.length?"flex":"none",z.style.display="none",y.querySelector(".furion-codeBox-point")?J.replaceChild(X,y.querySelector(".furion-codeBox-point")):J.appendChild(X),eA(),y.querySelector(".furion-submit").style.display="block",y.querySelector(".furion-query").style.display="block"}else!function(A,e,t,n,r,o,i,s,a,c,u,d,B){y.querySelector(".furion-codeBox").innerHTML='<div class="furion-codeBox-point">\n    <p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">元素匹配类型：</span><span fr_atr="furion-node" class="furion-point-txt"/>'.concat(S[d],'</span>\n        </p>\n        <p class="furion-codeBox-item eid-txt" fr_atr=\'furion-node\'><span class="furion-point-txt ">元素id：</span><span fr_atr="furion-node" class="furion-point-txt "/>').concat(B,'</span>\n        </p>\n    <p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">埋点名称：</span><span fr_atr="furion-node" class="furion-point-txt"/>').concat(r,'</span>\n        </p><p class="furion-codeBox-item fx-txt" fr_atr=\'furion-node\'><span class="furion-point-txt ">埋点元素：').concat(n||"--",'</span></p><p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">埋点事件：').concat(e||"--",'</span></p><p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">埋点元素个数：').concat(y.querySelectorAll(".function-dom-active").length,'</span></p><p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">埋点路径：').concat(i||"--",'</span></p><p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">埋点文案：').concat("null"!==s&&s?s:"--",'</span></p><p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">页面匹配类型：').concat(""===c?"--":F[c],'</span></p><p class="furion-codeBox-item" fr_atr=\'furion-node\'><span class="furion-point-txt">页面匹配规则：').concat(u||"--","</span></p></div>"),y.querySelector(".furion-submit").style.display="none",y.querySelector(".furion-query").style.display="none",d===l.CODELESS_CONFIG.noPointEventTypeMap.id?y.querySelector(".fx-txt").style.display="none":y.querySelector(".eid-txt").style.display="none"}(0,e,0,o,i,s,a,u,0,B,f,p,h);y.getElementById("pop-furion").style.display="block"},setPostMessage:aA}},"./src/codeLessConfig.js":function(A,e,t){"use strict";t.r(e),t.d(e,{CODELESS_CONFIG:function(){return n}});var n={DOMS_TEXT_LIST:[],noPointEventTypeMap:{xpath:"0",id:"1",tc:"2"}}},"./src/config.js":function(A,e,t){"use strict";t.r(e),t.d(e,{CONFIG:function(){return n}});var n={IS_IFRAME:!1,COOKIES:{ssid:"_fr_ssid",frid:"_frid",w3Fid:"_w3Fid"},SPECIAL_CHARS:/[,，;；.。'‘’:："“”]/g,THRESHOLD:{CLICK_INFO:20,LOG_INFO:80,TOO_MUCH_JS_ERR:100},MAX_LEN:{JS_ERR_VAL:4e3},LOG_TYPE:{click:"cl",performance:"pe",page:"pv",focus:"fo",error:"er",api:"api",start:"st",end:"ed",custom:"cm",operation:"opt",info:"info",longtask:"lt",resource:"static",componentConfig:"cc",fps:"fps",crash:"crash",ws:"ws"},THIRD_PARTY_DOMAIN:["google-analytics.com","uba.huaweicloud.com","bi.huaweicloud.com"],IMG_SUFFIX:["svg","png","jpg","jpeg","gif"],GLOBAL_VARS:{VERSION:"3.6.52",logList:[],cacheList:[],lastUrl:"",preUrl:"",lastPvid:"",lastSsid:"",sendTimer:null,csidObj:{},storeObj:{},lastClickInfo:[],lastReportDate:(new Date).getDate(),eno:"",appId:"",domain:"",setting:"",randomId:"",fmp:-1,IAMInfo:null,isAvailableCookie:Boolean(document.domain),isWeCodeApp:Boolean(window.HWH5),cp:["devui-dropdown-menu","cdk-overlay-container","ztree","cida-ui","devui-link","devui-logs","newHeader","settings-menus","header-container","footer-nav","footer-hot-recommend","footer-service","footer-copyright-container","graph"],smartJsErr:!0,checkComponentConfig:!1,ABtestInfo:null},VISIT_INFO:{os:"",pid:"",pvid:"",browser:""},FPS_REQUEST_ID:0}},"./src/consistency.js":function(A,e,t){"use strict";t.r(e),t.d(e,{Consistency:function(){return B}});var n=t("./src/util.js"),r=t("./src/newToast.js"),o=t("./src/domActions.js"),i=t("./src/newNav.js"),s=t("./src/style.js"),a=null,c=[],u=!1;function l(){var A;(c||[]).forEach((function(A){var e=A.selector,t=null;if(e&&(t=n.UTIL.getEleOfSelector(e)),t){var r=o.DOMACTIONS.getBoundingClientRect(t),i=r.left,s=r.top,a=r.width,c=r.height;A.top=s,A.left=i,A.width=a,A.height=c}})),c=c.filter((function(A){var e=A.top,t=A.left,n=A.width,r=A.height;return void 0!==t&&void 0!==e&&n&&r})).sort((function(A,e){return e.width*e.height-A.width*A.height})),A=o.DOMACTIONS.createNode("div","furion-consistency-dom-container"),c.forEach((function(e){var t=e.top,n=e.left,r=e.width,i=e.height,s=e.suggestion,a=e.elementKey,c=e.elementValue,u=e.fx;s&&(u="".concat(u,":").concat(s)),c&&(u="".concat(u,"; ").concat(a,":").concat(c));var l=o.DOMACTIONS.createNode("div","furion-consistency-dom","","","","",r,i,n,t,u);A.appendChild(l)})),a.appendChild(A)}function d(){var A,e=null===(A=a)||void 0===A?void 0:A.querySelector(".furion-consistency-dom-container");e&&e.remove()}var B={runConsistency:function(A){u||(a=document.querySelector(".code-less-box").shadowRoot,d(),function(A){u=!0,c=[],a.querySelector("#furion-loading").style.display="block";var e=A===i.NEWNAV.consistencyModeEnum.offline?(t=n.UTIL.getConfig().searchDomain,o=a.querySelector(".start-time").value,d=a.querySelector(".end-time").value,B=a.querySelector("#consistencySelect").value,g=a.querySelector(".consistency-input").value.trim(),f={appId:n.UTIL.getAppId(),url:n.UTIL.getReportUrl(),sTime:o,eTime:d,category:B,fx:g},n.UTIL.forbidClick(),n.UTIL.httpRequest("".concat(t,"/style/xpathDetail"),"post",f,2e4)):function(){var A=(0,s.getNodeStyles)([],[]).map((function(A){var e=A.attributes;return(void 0===e?[]:e).forEach((function(e){var t=e.key,n=e.value;A[t]=n})),{selector:A.selector,fx:A.fx,enm:A.enm,eid:A.eid,tc:A.tc,ecls:A.ecls,color:A.color,fontSize:A.fontSize,borderRadius:A.borderRadius,boxShadow:A.boxShadow,padding:A.padding}})),e=a.querySelector("#consistencySelect").value,t=a.querySelector(".consistency-input").value.trim(),r=n.UTIL.getConfig().searchDomain,o=n.UTIL.getAppId(),i=n.UTIL.getReportUrl(),c={styleList:A,fx:t};return n.UTIL.httpRequest("".concat(r,"/style/check?appId=").concat(o,"&url=").concat(i,"&category=").concat(e),"post",c,2e4)}();var t,o,d,B,g,f;e.then((function(A){var e=A||{},t=e.result,n=void 0===t?{}:t,o=e.status;"success"===(void 0===o?"":o)?(c=n.data||[],l()):r.TOAST.toast(n.message||"查询一致性不规范数据失败",3e3)})).catch((function(A){r.TOAST.toast("查询一致性不规范数据失败",3e3)})).finally((function(){a.querySelector("#furion-loading").style.display="none",u=!1,n.UTIL.recoverClick()}))}(A))},renderConsistency:l,removeConsistencyDom:d}},"./src/crash.js":function(A,e,t){"use strict";t.r(e),t.d(e,{CRASH:function(){return h}});var n=t("./src/config.js"),r=t("./src/util.js"),o=t("./src/log.js"),i=t("./src/workerScript.js"),s=t("./node_modules/uuid/dist/esm-browser/v4.js");function a(A){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},a(A)}function c(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function u(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?c(Object(t),!0).forEach((function(e){l(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function l(A,e,t){return(e=function(A){var e=function(A,e){if("object"!=a(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var n=t.call(A,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==a(e)?e:e+""}(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}var d=function(){var A=new Blob([i.default],{type:"application/javascript"}),e=new Worker(URL.createObjectURL(A));window.furionWorker=e,e.postMessage(u(u({},p("register")),{},{crashId:(0,s.default)()})),e.onmessage=function(A){var t=p("reply",A);A.data.crashId||(t.crashId=(0,s.default)()),e.postMessage(t)}},B=function(){window.furionWorker.terminate(),window.furionWorker=null},g=function(A,e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState?window.furionWorker&&e():"visible"===document.visibilityState&&(window.furionWorker||A())}),!0)},f=function(){try{var A=performance.memory;return{ths:A.totalJSHeapSize,uhs:A.usedJSHeapSize,lhs:A.jsHeapSizeLimit,ts:(new Date).getTime()}}catch(A){return null}},p=function(A,e){var t;return{queryStr:o.LOG.getQueryStr(r.UTIL.getReportUrl()),domain:n.CONFIG.GLOBAL_VARS.domain,type:A,checkTime:(null==e||null===(t=e.data)||void 0===t?void 0:t.checkTime)||(new Date).getTime(),jsHeap:f()}},h={crash:function(){r.UTIL.validateWorker()&&("visible"===document.visibilityState&&d(),g(d,B))},getJSHeap:f}},"./src/domActions.js":function(A,e,t){"use strict";t.r(e),t.d(e,{DOMACTIONS:function(){return f}});var n=t("./src/codeLessConfig.js");function r(A){return function(A){if(Array.isArray(A))return o(A)}(A)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(A)||function(A,e){if(A){if("string"==typeof A)return o(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?o(A,e):void 0}}(A)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function i(A){var e=A.getBoundingClientRect();return{width:e.width||e.right-e.left,height:e.height||e.bottom-e.top,left:e.left,top:e.top}}function s(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"",i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"",s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"",a=arguments.length>8&&void 0!==arguments[8]?arguments[8]:"",c=arguments.length>9&&void 0!==arguments[9]?arguments[9]:"",u=arguments.length>10&&void 0!==arguments[10]?arguments[10]:"";if(!A)return null;var l=document.createElement(A);return""!==e&&(l.className=e),""!==r&&l.setAttribute("href",r),""!==n&&(l.innerHTML=n),""!==t&&l.setAttribute("id",t),""!==o&&l.setAttribute("src",o),""!==i&&(l.style.width=i+"px"),""!==s&&(l.style.height=s+"px"),""!==c&&(l.style.top=c+"px"),""!==a&&(l.style.left=a+"px"),""!==u&&l.setAttribute("title",u),l.setAttribute("fr_atr","furion-node"),l}function a(A){return A?document.evaluate(A,document,null,XPathResult.ANY_TYPE,null).iterateNext():null}function c(A){var e=document.querySelector(".code-less-box").shadowRoot,t=e.querySelectorAll(".function-dom-".concat(A)),n=e.querySelector("#".concat(A,"-dom-parentBox"));if(n)n.remove();else{if(0===t.length)return;t[0].remove()}}function u(A,e){var t;if(!A)return!1;var n=i(A),r=n.width,o=n.height,a=n.left,c=n.top,u=s("div","function-dom-".concat(e),"","","","",r,o,a,c);return u.setAttribute("dom_tc",null===(t=A.innerText)||void 0===t?void 0:t.trim()),u}function l(A,e,t,n,r,o,i,s){for(var c,d=A.split("/").filter((function(A){return""!==A})),B=d.slice(0,e[t]).join("/"),g=d.slice(e[t]+1,d.length).join("/"),f=a(B),p=null===(c=d[e[t]])||void 0===c?void 0:c.substring(0,d[e[t]].indexOf("[")),h=null===f?[]:f.getElementsByTagName(p),w=0;w<h.length;w++){var C=g?"".concat(B,"/").concat(p,"[").concat(w+1,"]/").concat(g):"".concat(B,"/").concat(p,"[").concat(w+1,"]");if(t+1===n){var Q,m=a(C);m&&m.innerText&&i.push(m.innerText.trim());var U=u(m,o);!U||s&&"null"!==s&&s!==(null===(Q=m.innerText)||void 0===Q?void 0:Q.trim())||r.appendChild(U)}else l(C,e,t+1,n,r,o,i,s)}}function d(A,e,t){var n;c(e),c("hover");var r=document.querySelector(".code-less-box").shadowRoot,o=document.getElementById(A),i=u(o,e);return!i||"marked"===e||t&&"null"!==t&&t!==(null===(n=o.innerText)||void 0===n?void 0:n.trim())?i:(r.appendChild(i),o)}function B(){var A=document.querySelector(".code-less-box").shadowRoot.querySelector(".furion-marked-box");A&&A.remove()}function g(A){for(var e=A.split("/").filter((function(A){return""!==A})),t=["svg","g","path","circle","polygon","use"],n=0;n<e.length;n++){var r=e[n],o=r.indexOf("[");e[n]=o>-1?t.includes(r.substring(0,o))?"*[local-name()='".concat(r.substring(0,o),"']").concat(r.substring(o)):r:t.includes(r)?"*[local-name()='".concat(r,"']"):r}return e.join("/")}var f={createNode:s,addCSS:function(A){var e=document.createElement("style"),t=document.head||document.getElementsByTagName("head")[0];if(e.type="text/css",e.styleSheet){var n=function(){try{e.styleSheet.cssText=A}catch(A){n()}};e.styleSheet.disabled?setTimeout(n,10):n()}else{var r=document.createTextNode(A);e.appendChild(r)}t.appendChild(e)},addCodeLessCSS:function(A){var e=document.createElement("style"),t=document.querySelector(".code-less-box").shadowRoot;if(e.type="text/css",e.styleSheet){var n=function(){try{e.styleSheet.cssText=A}catch(A){n()}};e.styleSheet.disabled?setTimeout(n,10):n()}else{var r=document.createTextNode(A);e.appendChild(r)}t.appendChild(e)},getBoundingClientRect:i,removeActive:c,getPeersDom:function(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:-1,t=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=[],d=document.querySelector(".code-less-box").shadowRoot;if(c(t),c("hover"),A){var B=g(A);if(-1!==e){var f=s("div","","".concat(t,"-dom-parentBox"));l(B,e,0,e.length,f,t,i,o),d.appendChild(f)}else{var p=a(B);p&&p.innerText&&i.push(p.innerText.trim());var h=u(p,t);h&&d.appendChild(h),d.querySelector("#pop-furion").classList.add("pop-shake"),setTimeout((function(){d.querySelector("#pop-furion").classList.remove("pop-shake")}),500)}n.CODELESS_CONFIG.DOMS_TEXT_LIST=r(new Set(i))}},createActive:u,getDomByXpath:a,getDomById:d,showAllMarkedDom:function(A){B();var e=document.querySelector(".code-less-box").shadowRoot,t=s("div","furion-marked-box"),r=[],o=[];A.forEach((function(A){A.noPointEventType===n.CODELESS_CONFIG.noPointEventTypeMap.id?o.push(A):A.noPointEventType===n.CODELESS_CONFIG.noPointEventTypeMap.xpath&&r.push(A)})),function(A,e){for(var t=0;t<A.length;t++){var n=A[t],r=n.fx,o=n.fuzzyIndexArr,i=n.tc;if(o.length)l(r,o,0,o.length,e,"marked",[],i);else{var s=u(a(r),"marked");s&&e.appendChild(s)}}}(function(A){var e=A.map((function(A){return{fuzzyIndexArr:(A.fuzzys||[]).map((function(A,e){return A?e:0})).filter((function(A){return A})),fx:g(A.fx),xpath:A.xpath,tc:A.tc||""}}));return e}(r),t),function(A,e){A.forEach((function(A){var t=d(A.eid,"marked",A.tc);t&&e.appendChild(t)}))}(o,t),e.appendChild(t)},removeMarkedDom:B}},"./src/fetch.js":function(A,e,t){"use strict";t.r(e),t.d(e,{FETCH:function(){return g}});var n=t("./src/apiUtil.js"),r=t("./src/log.js"),o=t("./src/util.js"),i=t("./src/config.js");function s(A){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},s(A)}function a(){a=function(){return e};var A,e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},o=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function u(A,e,t,n){return Object.defineProperty(A,e,{value:t,enumerable:!n,configurable:!n,writable:!n})}try{u({},"")}catch(A){u=function(A,e,t){return A[e]=t}}function l(e,t,n,r){var o=t&&t.prototype instanceof g?t:g,i=Object.create(o.prototype);return u(i,"_invoke",function(e,t,n){var r=1;return function(o,i){if(3===r)throw Error("Generator is already running");if(4===r){if("throw"===o)throw i;return{value:A,done:!0}}for(n.method=o,n.arg=i;;){var s=n.delegate;if(s){var a=v(s,n);if(a){if(a===B)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(1===r)throw r=4,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=3;var c=d(e,t,n);if("normal"===c.type){if(r=n.done?4:2,c.arg===B)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r=4,n.method="throw",n.arg=c.arg)}}}(e,n,new I(r||[])),!0),i}function d(A,e,t){try{return{type:"normal",arg:A.call(e,t)}}catch(A){return{type:"throw",arg:A}}}e.wrap=l;var B={};function g(){}function f(){}function p(){}var h={};u(h,o,(function(){return this}));var w=Object.getPrototypeOf,C=w&&w(w(b([])));C&&C!==t&&n.call(C,o)&&(h=C);var Q=p.prototype=g.prototype=Object.create(h);function m(A){["next","throw","return"].forEach((function(e){u(A,e,(function(A){return this._invoke(e,A)}))}))}function U(A,e){function t(r,o,i,a){var c=d(A[r],A,o);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==s(l)&&n.call(l,"__await")?e.resolve(l.__await).then((function(A){t("next",A,i,a)}),(function(A){t("throw",A,i,a)})):e.resolve(l).then((function(A){u.value=A,i(u)}),(function(A){return t("throw",A,i,a)}))}a(c.arg)}var r;u(this,"_invoke",(function(A,n){function o(){return new e((function(e,r){t(A,n,e,r)}))}return r=r?r.then(o,o):o()}),!0)}function v(e,t){var n=t.method,r=e.i[n];if(r===A)return t.delegate=null,"throw"===n&&e.i.return&&(t.method="return",t.arg=A,v(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),B;var o=d(r,e.i,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,B;var i=o.arg;return i?i.done?(t[e.r]=i.value,t.next=e.n,"return"!==t.method&&(t.method="next",t.arg=A),t.delegate=null,B):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,B)}function y(A){this.tryEntries.push(A)}function F(e){var t=e[4]||{};t.type="normal",t.arg=A,e[4]=t}function I(A){this.tryEntries=[[-1]],A.forEach(y,this),this.reset(!0)}function b(e){if(null!=e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=A,t.done=!0,t};return i.next=i}}throw new TypeError(s(e)+" is not iterable")}return f.prototype=p,u(Q,"constructor",p),u(p,"constructor",f),f.displayName=u(p,c,"GeneratorFunction"),e.isGeneratorFunction=function(A){var e="function"==typeof A&&A.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(A){return Object.setPrototypeOf?Object.setPrototypeOf(A,p):(A.__proto__=p,u(A,c,"GeneratorFunction")),A.prototype=Object.create(Q),A},e.awrap=function(A){return{__await:A}},m(U.prototype),u(U.prototype,i,(function(){return this})),e.AsyncIterator=U,e.async=function(A,t,n,r,o){void 0===o&&(o=Promise);var i=new U(l(A,t,n,r),o);return e.isGeneratorFunction(t)?i:i.next().then((function(A){return A.done?A.value:i.next()}))},m(Q),u(Q,c,"Generator"),u(Q,o,(function(){return this})),u(Q,"toString",(function(){return"[object Generator]"})),e.keys=function(A){var e=Object(A),t=[];for(var n in e)t.unshift(n);return function A(){for(;t.length;)if((n=t.pop())in e)return A.value=n,A.done=!1,A;return A.done=!0,A}},e.values=b,I.prototype={constructor:I,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=A,this.done=!1,this.delegate=null,this.method="next",this.arg=A,this.tryEntries.forEach(F),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=A)},stop:function(){this.done=!0;var A=this.tryEntries[0][4];if("throw"===A.type)throw A.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(A){i.type="throw",i.arg=e,t.next=A}for(var r=t.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r],i=o[4],s=this.prev,a=o[1],c=o[2];if(-1===o[0])return n("end"),!1;if(!a&&!c)throw Error("try statement without catch or finally");if(null!=o[0]&&o[0]<=s){if(s<a)return this.method="next",this.arg=A,n(a),!0;if(s<c)return n(c),!1}}},abrupt:function(A,e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[0]>-1&&n[0]<=this.prev&&this.prev<n[2]){var r=n;break}}r&&("break"===A||"continue"===A)&&r[0]<=e&&e<=r[2]&&(r=null);var o=r?r[4]:{};return o.type=A,o.arg=e,r?(this.method="next",this.next=r[2],B):this.complete(o)},complete:function(A,e){if("throw"===A.type)throw A.arg;return"break"===A.type||"continue"===A.type?this.next=A.arg:"return"===A.type?(this.rval=this.arg=A.arg,this.method="return",this.next="end"):"normal"===A.type&&e&&(this.next=e),B},finish:function(A){for(var e=this.tryEntries.length-1;e>=0;--e){var t=this.tryEntries[e];if(t[2]===A)return this.complete(t[4],t[3]),F(t),B}},catch:function(A){for(var e=this.tryEntries.length-1;e>=0;--e){var t=this.tryEntries[e];if(t[0]===A){var n=t[4];if("throw"===n.type){var r=n.arg;F(t)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={i:b(e),r:t,n},"next"===this.method&&(this.arg=A),B}},e}function c(A,e,t,n,r,o,i){try{var s=A[o](i),a=s.value}catch(A){return void t(A)}s.done?e(a):Promise.resolve(a).then(n,r)}function u(A){return function(){var e=this,t=arguments;return new Promise((function(n,r){var o=A.apply(e,t);function i(A){c(o,n,r,i,s,"next",A)}function s(A){c(o,n,r,i,s,"throw",A)}i(void 0)}))}}function l(A,e,t){if(A.ok||r.LOG.FURION.log("submitCache"),o.UTIL.apiCanReport(A.url)&&!A.url.includes(i.CONFIG.GLOBAL_VARS.domain)){var s=o.UTIL.getApiPathWithQuery(A.url);r.LOG.report("api",s,function(A,e,t){var r,i=A.status,s=A.statusText,a=A.url,c=A.headers,u=t,l=(0,n.getApiPerformance)(a),d=l.fs,B=l.dr,g=l.stt,f=l.protocol,p=l.transfer_size,h=o.UTIL.getConfig().customApiMsg;h&&(u=h(i,s,a));var w=function(A){var e=new Map;return A.headers.forEach((function(A,t){e.set(t,A)})),e}(A),C=o.UTIL.getReportHeaders(w),Q=e||{},m=Q.body,U=Q.method,v={mtd:(void 0===U?"GET":U).toUpperCase(),tid:(0,n.getTraceId)(c),fs:d,dr:B,stt:g,protocol:f,sc:i,msg:null===(r=u)||void 0===r?void 0:r.substr(0,o.UTIL.limitLen),transfer_size:p,headers:C},y=o.UTIL.getReportParams(a,m);y&&(v.info=y);return v}(A,e,t))}}function d(A){return B.apply(this,arguments)}function B(){return(B=u(a().mark((function A(e){var t,n,r,i,s,c,u;return a().wrap((function(A){for(;;)switch(A.prev=A.next){case 0:if(t=(null==e?void 0:e.statusText)||"",A.prev=1,i=null===(n=e.headers.get("content-type"))||void 0===n?void 0:n.includes("application/json"),s=null===(r=e.headers.get("content-type"))||void 0===r?void 0:r.includes("text/plain"),!i){A.next=11;break}return A.next=7,e.clone().json();case 7:c=A.sent,o.UTIL.responseHasErr(e.status,c)&&(t=JSON.stringify(c)),A.next=16;break;case 11:if(!s){A.next=16;break}return A.next=14,e.clone().text();case 14:u=A.sent,t=u;case 16:A.next=20;break;case 18:A.prev=18,A.t0=A.catch(1);case 20:return A.abrupt("return",Promise.resolve(t));case 21:case"end":return A.stop()}}),A,null,[[1,18]])})))).apply(this,arguments)}var g={proxyFetch:function(){var A=window.fetch;A&&(window.fetch=u(a().mark((function e(){var t,n,o,i,s,c,u,B=arguments;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t=B.length,n=new Array(t),o=0;o<t;o++)n[o]=B[o];return i=n[0],s=n[1],e.prev=2,e.next=5,A(i,s);case 5:if(!(c=e.sent).url){e.next=11;break}return e.next=9,d(c);case 9:u=e.sent,setTimeout((function(){l(c,s,u)}),100);case 11:return e.abrupt("return",c);case 14:return e.prev=14,e.t0=e.catch(2),r.LOG.FURION.log("submitCache"),e.abrupt("return",Promise.reject(e.t0));case 18:case"end":return e.stop()}}),e,null,[[2,14]])}))))}}},"./src/finger-print.js":function(A,e,t){"use strict";t.r(e),t.d(e,{setRandomId:function(){return i}});var n=t("./node_modules/uuid/dist/esm-browser/v4.js"),r=t("./src/config.js"),o=t("./src/util.js");function i(){if(o.UTIL.getCookie(r.CONFIG.COOKIES.frid))return o.UTIL.getCookie(r.CONFIG.COOKIES.frid);var A=(0,n.default)().split("-").join("");return o.UTIL.setCookie(r.CONFIG.COOKIES.frid,A,525600,!0),A}},"./src/fmp.js":function(A,e,t){"use strict";function n(A){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},n(A)}function r(A,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(A,o(n.key),n)}}function o(A){var e=function(A,e){if("object"!=n(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var r=t.call(A,e||"default");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==n(e)?e:e+""}t.r(e),t.d(e,{default:function(){return s}});var i=function(){return A=function A(){!function(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}(this,A)},t=[{key:"getFmp",value:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3e3;return Promise&&window.performance&&window.performance.timing&&window.requestAnimationFrame&&window.MutationObserver||Promise.reject(new Error("fmp can not be retrieved")),new Promise((function(e){var t=[],n=new window.MutationObserver((function(){var A=window.innerHeight;function e(t,n){var r=t.children?t.children.length:0,o=0,i=t.tagName;if("SCRIPT"!==i&&"STYLE"!==i&&"META"!==i&&"HEAD"!==i&&(t.getBoundingClientRect&&t.getBoundingClientRect().top<A&&(o+=n*r),r>0))for(var s=t.children,a=0;a<r;a++)o+=e(s[a],n+1);return o}window.requestAnimationFrame((function(){var A=window.performance.timing.fetchStart,n=(new Date).getTime()-A,r=e(document,1);t.push({score:r,t:n})}))}));n.observe(document,{childList:!0,subtree:!0}),setTimeout((function(){n.disconnect();for(var r=[],o=1;o<t.length;o++)t[o].t!==t[o-1].t&&r.push({t:t[o].t,rate:t[o].score-t[o-1].score});r.sort((function(A,e){return e.rate-A.rate})),r.length>0?e(r[0].t):e(A)}),A)}))}}],(e=null)&&r(A.prototype,e),t&&r(A,t),Object.defineProperty(A,"prototype",{writable:!1}),A;var A,e,t}();const s=i},"./src/fps.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return s}});var n=t("./src/log.js"),r=t("./node_modules/uuid/dist/esm-browser/v4.js"),o=t("./src/util.js"),i=t("./src/config.js");const s=function(){var A=o.UTIL.getConfig().setting,e=void 0===A?"":A;if(window.requestAnimationFrame&&!e.includes("forbidFps")){var t=performance.now(),s=0,a=[],c=0,u="",l=function(){s++;var A=performance.now();if(A-t>1e3){var e=Math.round(1e3*s/(A-t)),o={val:e,ts:(new Date).getTime()};a.length>=3&&a.shift(),a.push(o);var d=3===a.length&&a.every((function(A){return A.val>=30}));if(c<100){if(3===a.length&&a.every((function(A){return A.val<30}))&&!u){u=(0,r.default)();for(var B=0;B<a.length-1;B++){var g=a[B],f=g.val,p=g.ts;n.LOG.report("fps",f,{ts:p,fid:u}),c++,B===a.length-2&&n.LOG.report("fps",-1,{fid:u,ts:a[0].ts})}}u?(n.LOG.report("fps",e,{fid:u}),c++):c=0}else if(u&&d)for(var h=0;h<a.length;h++){var w=a[h],C=w.val,Q=w.ts;n.LOG.report("fps",C,{ts:Q,fid:u})}d&&u&&(n.LOG.report("fps",-2,{fid:u}),u=""),s=0,t=A}i.CONFIG.FPS_REQUEST_ID=window.requestAnimationFrame(l)};l()}}},"./src/heat-new.js":function(A,e,t){"use strict";t.r(e),t.d(e,{uploadPageDom:function(){return n}});var n=function(A){console.warn("Furion：3.6.51及以后的版本不支持上报dom")};window.__fr&&(window.__fr.uploadTheDom=n)},"./src/heatMap.js":function(A,e,t){"use strict";t.r(e),t.d(e,{Heatmap:function(){return m}});var n=t("./src/scripts/heatmap.min.js"),r=t.n(n),o=t("./src/util.js"),i=t("./src/newToast.js"),s=(t("./src/config.js"),t("./src/domActions.js"));function a(A){return function(A){if(Array.isArray(A))return c(A)}(A)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(A)||function(A,e){if(A){if("string"==typeof A)return c(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(A,e):void 0}}(A)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}var u=[{id:"selector",name:"selector"},{id:"clicks",name:"点击次数"},{id:"uvCount",name:"点击用户数"},{id:"clickRatio",name:"点击占比",isRate:!0},{id:"clickRate",name:"点击率（点击次数/页面PV）",isRate:!0}],l="center",d="bottom",B="top",g=null,f={data:[]},p=null,h=[],w=[];function C(){g&&g._renderer._clear()}function Q(A){var e=A.left,t=A.top,n=A.width,r=A.height;return"".concat(e,"-").concat(t,"-").concat(n,"-").concat(r)}var m={runHeatmap:function(){p=document.querySelector(".code-less-box").shadowRoot,function(){var A=p.querySelector("#dom_based_heatmap_container"),e=A.querySelector(".heatmap-canvas");e&&e.remove();var t=A.querySelector(".furion-hover-dom-container");t&&t.remove();g=r().create({container:A,backgroundColor:"rgba(0,0,0,.2)"})}(),function(){var A=o.UTIL.getConfig().searchDomain;p.querySelector("#furion-loading").style.display="block";var e=p.querySelector(".start-time").value,t=p.querySelector(".end-time").value;h=[];var n={appId:o.UTIL.getAppId(),url:o.UTIL.getReportUrl(),startTime:e,endTime:t};C(),o.UTIL.forbidClick(),o.UTIL.httpRequest("".concat(A,"/heatmap/xpathNew"),"post",n,1e4).then((function(A){if("success"===(null==A?void 0:A.status)){var e,t=(h=(null===(e=A.result)||void 0===e?void 0:e.domClickInfos)||[]).length?"查询热力数据成功":"暂无热力数据";i.TOAST.toast(t,3e3),b="--selector--",h.forEach((function(A){var e=A.xpath,t=void 0===e?"":e;A.selector=t,t.indexOf(b)>=0&&(A.selector=t.split(b)[1]),delete A.xpath})),F="selector",I=new Map,function(A){A.forEach((function(A){var e=A.selector,t=A.xpath;A.radius=20;var n=null;if(e&&(n=o.UTIL.getEleOfSelector(e)),t&&(n=o.UTIL.getEleOfXpath(t)),n){var r=function(A){var e=A.getBoundingClientRect(),t=e.left,n=e.top,r=e.width,o=e.height;return{left:Math.round(t),top:Math.round(n),width:Math.round(r),height:Math.round(o)}}(n),i=r.left,s=r.top,a=r.width,c=r.height;A.top=s,A.left=i,A.width=a,A.height=c}}))}(h=h.filter((function(A){return!I.has(A[F])&&I.set(A[F],1)}))),h=h.filter((function(A){var e=A.top,t=A.left,n=A.width,r=A.height;return void 0!==t&&void 0!==e&&n&&r})).sort((function(A,e){var t=A.width*A.height+e.clicks;return e.width*e.height+A.clicks-t})),y="--selector--",h.forEach((function(A){if(A.selector){var e=Q(A);h.find((function(A){return A.xpath&&Q(A)===e}))&&(A.selector=y)}})),h=h.filter((function(A){return(null==A?void 0:A.selector)!==y})),v=h.map((function(A){return A.clicks})).reduce((function(A,e){return A+e}),0),h.forEach((function(A){var e=A.clicks;A.value=o.UTIL.twoDecimalDivide(e,v),A.clickRatio=A.value})),function(){var A=h.map((function(A){var e=A.left,t=A.top,n=A.value,r=A.width,o=A.height;return{x:Math.ceil(e+r/2),y:Math.ceil(t+o/2),value:n,radius:20}})),e=Math.min.apply(Math,a(A.map((function(A){return A.value})))),t=Math.max.apply(Math,a(A.map((function(A){return A.value}))));f={data:A,min:e,max:t};var n=o.UTIL.getPageWidth(),r=o.UTIL.getPageHeight();setTimeout((function(){if(g){g._renderer.setDimensions(n,r),g.setData(f);var A=p.querySelector("#dom_based_heatmap_container");A.style.width="100vw",A.style.height="100vh",o.UTIL.recoverClick()}}),100)}(),r={},function(){var A=h.map((function(A){var e={left:A.left,top:A.top,width:A.width,height:A.height,formatedFx:A.formatedFx};return Object.assign(e,function(A){var e={};return u.forEach((function(t){var n=t.id,r=t.isRate;e[n]=r?"".concat(A[n]," %"):A[n]})),e}(A)),e}));return A}().forEach((function(A){r[A.selector]?r[A.selector][u[1].id]="".concat(r[A.selector][u[1].id]||"",",").concat(A[u[1].id]):r[A.selector]=A})),(w=Object.values(r)).forEach((function(A){A.hoverList=function(A){return u.map((function(e){return{name:e.name,value:A[Object.keys(A).find((function(A){return A===e.id}))]}}))}(A)})),c=400,C=300,m=p.querySelector("#dom_based_heatmap_container"),U=s.DOMACTIONS.createNode("div","furion-hover-dom-container"),w.forEach((function(A){var e=A||{},t=e.width,n=void 0===t?0:t,r=e.height,o=void 0===r?0:r,i=e.top,a=void 0===i?0:i,u=e.left,g=void 0===u?0:u,f=s.DOMACTIONS.createNode("div","heatmap-hover-box","","","","",n,o,g,a),p=s.DOMACTIONS.createNode("div","furion-hover-border-dom","","","","",n,o),h=function(A,e,t,n,r,o){var i=function(A,e,t,n){var r=window.innerWidth||document.documentElement.clientWidth,o=window.innerHeight||document.documentElement.clientHeight,i=t+e/2,s=o-i,a=n+A/2,c=r-a;return{topDistance:i,bottomDistance:s,leftDistance:a,rightDistance:c}}(A,e,t,n),s=(i.topDistance,i.bottomDistance),a=i.leftDistance,c=i.rightDistance,u=(A-r)/2,g=4,f=d;return r/2>a&&(u=0),r/2>c&&(u=A-r),o>s-e/2&&(o>e/2?(g=-(e+o+4),f=B):(g=-4-e/2,f=l)),{infoDomLeft:u,infoDomTop:g,infoPos:f}}(n,o,a,g,c,C),w=h.infoDomLeft,Q=h.infoDomTop,m=h.infoPos,v=s.DOMACTIONS.createNode("div","furion-hover-info-dom","","","","",c,C,w,Q),y=0,F=!1,I=0;m===B?(y=-2,F=!0,I=C):m===l?(y=o/2,I=-4):(y=o-2,I=-4);var b=s.DOMACTIONS.createNode("div","heatmap-triangle","","","","",0,0,n/2-4,y);F&&(b.style.transform="rotate(180deg)");var E=s.DOMACTIONS.createNode("div","info-connect-dom","","","","",c,4,0,I);v.appendChild(E),function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1?arguments[1]:void 0;A.forEach((function(A){var t=s.DOMACTIONS.createNode("div","info-content-dom","","".concat(A.name,"：").concat(A.value));e.appendChild(t)}))}(null==A?void 0:A.hoverList,v),p.appendChild(b),f.appendChild(p),f.appendChild(v),U.appendChild(f)})),m.append(U)}else{var n;i.TOAST.toast((null==A||null===(n=A.result)||void 0===n?void 0:n.message)||"查询热力数据失败",3e3),o.UTIL.recoverClick()}var r,c,C,m,U,v,y,F,I,b})).catch((function(A){i.TOAST.toast("查询热力数据失败",3e3),o.UTIL.recoverClick()})).finally((function(){p.querySelector("#furion-loading").style.display="none"}))}()},clearHeatMap:C}},"./src/images/back-arrow.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMThweCIgaGVpZ2h0PSIxOHB4IiB2aWV3Qm94PSIwIDAgMTggMTgiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3ctYmFjazwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSJQYWdlLTEiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSJhcnJvdy1iYWNrIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlBhdGgiIHBvaW50cz0iMCAwIDE4IDAgMTggMTggMCAxOCI+PC9wb2x5Z29uPgogICAgICAgICAgICA8cG9seWdvbiBmaWxsPSIjZmZmIiBwb2ludHM9IjE4IDcuODc0OTk5OTggNC4zMDg3NDk5OSA3Ljg3NDk5OTk4IDEwLjU5NzUgMS41ODYyNDk5OSA4Ljk5OTk5OTk3IDAgMCA4Ljk5OTk5OTk3IDguOTk5OTk5OTcgMTggMTAuNTg2MjUgMTYuNDEzNzUgNC4zMDg3NDk5OSAxMC4xMjUgMTggMTAuMTI1Ij48L3BvbHlnb24+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4="},"./src/images/close.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aXBkX2Nsb3NlMTwvdGl0bGU+CiAgICA8ZyBpZD0iaXBkX2Nsb3NlMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMy4wMDAwMDAsIDMuMDAwMDAwKSIgZmlsbD0iIzcxNzU3RiIgZmlsbC1ydWxlPSJub256ZXJvIiBpZD0i6Lev5b6EIj4KICAgICAgICAgICAgPHBhdGggZD0iTS0wLjM1MzU1MzM5MSwtMC4zNTM1NTMzOTEgQy0wLjE3OTk4NzAzOSwtMC41MjcxMTk3NDIgMC4wODk0MzczNjI0LC0wLjU0NjQwNDg5MyAwLjI4NDMwNTUwMywtMC40MTE0MDg4NDEgTDAuMzUzNTUzMzkxLC0wLjM1MzU1MzM5MSBMMTAuMzUzNTUzNCw5LjY0NjQ0NjYxIEMxMC41NDg4MTU1LDkuODQxNzA4NzYgMTAuNTQ4ODE1NSwxMC4xNTgyOTEyIDEwLjM1MzU1MzQsMTAuMzUzNTUzNCBDMTAuMTc5OTg3LDEwLjUyNzExOTcgOS45MTA1NjI2NCwxMC41NDY0MDQ5IDkuNzE1Njk0NSwxMC40MTE0MDg4IEw5LjY0NjQ0NjYxLDEwLjM1MzU1MzQgTC0wLjM1MzU1MzM5MSwwLjM1MzU1MzM5MSBDLTAuNTQ4ODE1NTM2LDAuMTU4MjkxMjQ1IC0wLjU0ODgxNTUzNiwtMC4xNTgyOTEyNDUgLTAuMzUzNTUzMzkxLC0wLjM1MzU1MzM5MSBaIj48L3BhdGg+CiAgICAgICAgICAgIDxwYXRoIGQ9Ik05LjY0NjQ0NjYxLC0wLjM1MzU1MzM5MSBDOS44NDE3MDg3NiwtMC41NDg4MTU1MzYgMTAuMTU4MjkxMiwtMC41NDg4MTU1MzYgMTAuMzUzNTUzNCwtMC4zNTM1NTMzOTEgQzEwLjUyNzExOTcsLTAuMTc5OTg3MDM5IDEwLjU0NjQwNDksMC4wODk0MzczNjI0IDEwLjQxMTQwODgsMC4yODQzMDU1MDMgTDEwLjM1MzU1MzQsMC4zNTM1NTMzOTEgTDAuMzUzNTUzMzkxLDEwLjM1MzU1MzQgQzAuMTU4MjkxMjQ1LDEwLjU0ODgxNTUgLTAuMTU4MjkxMjQ1LDEwLjU0ODgxNTUgLTAuMzUzNTUzMzkxLDEwLjM1MzU1MzQgQy0wLjUyNzExOTc0MiwxMC4xNzk5ODcgLTAuNTQ2NDA0ODkzLDkuOTEwNTYyNjQgLTAuNDExNDA4ODQxLDkuNzE1Njk0NSBMLTAuMzUzNTUzMzkxLDkuNjQ2NDQ2NjEgTDkuNjQ2NDQ2NjEsLTAuMzUzNTUzMzkxIFoiPjwvcGF0aD4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg=="},"./src/images/events.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,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"},"./src/images/filter-active.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+5pON5L2cL2ZpbHRlcjwvdGl0bGU+CiAgICA8ZGVzYz5DcmVhdGVkIHdpdGggU2tldGNoLjwvZGVzYz4KICAgIDxnIGlkPSLmk43kvZwvZmlsdGVyIiBzdHJva2U9Im5vbmUiIHN0cm9rZS13aWR0aD0iMSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgICAgICA8ZyBpZD0iZmlsdGVyIiBmaWxsPSIjNWU3Y2UwIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlRyaWFuZ2xlLTMiIHBvaW50cz0iMTAuMDA4NTc3NSA3IDEwLjAwODU3NzUgMTUgNiAxMyA2IDcgMiAzIDIgMSAxNCAxIDE0IDMiPjwvcG9seWdvbj4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg=="},"./src/images/filter.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+aWNvbl9uZXcvZmlsdGVyPC90aXRsZT4KICAgIDxkZXNjPkNyZWF0ZWQgd2l0aCBTa2V0Y2guPC9kZXNjPgogICAgPGcgaWQ9Imljb25fbmV3L2ZpbHRlciIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9IiNmZjAiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZD0iTTUsOC40MTQyMTM1NiBMMSw0LjQxNDIxMzU2IEwxLDEgTDE1LDEgTDE1LDQuNDE0MjEzNTYgTDExLDguNDE0MjEzNTYgTDExLDE1LjI4MDc3NjQgTDUsMTMuNzgwNzc2NCBMNSw4LjQxNDIxMzU2IFogTTEzLDMgTDMsMyBMMywzLjU4NTc4NjQ0IEw3LDcuNTg1Nzg2NDQgTDcsMTIuMjE5MjIzNiBMOSwxMi43MTkyMjM2IEw5LDcuNTg1Nzg2NDQgTDEzLDMuNTg1Nzg2NDQgTDEzLDMgWiIgaWQ9IuefqeW9oiIgZmlsbD0iIzcxNzU3RiIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg=="},"./src/images/helping.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+6YeK5LmJ5Zu+5qCHL+mhtemdouWbvuaghy/luK7liqnpl67lj7c8L3RpdGxlPgogICAgPGRlc2M+Q3JlYXRlZCB3aXRoIFNrZXRjaC48L2Rlc2M+CiAgICA8ZyBpZD0i6YeK5LmJ5Zu+5qCHL+mhtemdouWbvuaghy/luK7liqnpl67lj7ciIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxnIGlkPSLluK7liqnpl67lj7ciPgogICAgICAgICAgICA8cGF0aCBkPSJNOC41LDguOTU4NTIwNzggTDguNSwxMSBMNy41LDExIEw3LjUsOC41IEM3LjUsOC4yMjM4NTc2MyA3LjcyMzg1NzYzLDggOCw4IEM5LjEwNDU2OTUsOCAxMCw3LjEwNDU2OTUgMTAsNiBDMTAsNC44OTU0MzA1IDkuMTA0NTY5NSw0IDgsNCBDNi44OTU0MzA1LDQgNiw0Ljg5NTQzMDUgNiw2IEw1LDYgQzUsNC4zNDMxNDU3NSA2LjM0MzE0NTc1LDMgOCwzIEM5LjY1Njg1NDI1LDMgMTEsNC4zNDMxNDU3NSAxMSw2IEMxMSw3LjQ4NjQ5ODE0IDkuOTE4ODU2NjcsOC43MjA0ODE3MyA4LjUsOC45NTg1MjA3OCBMOC41LDguOTU4NTIwNzggWiBNOCwxNiBDMy41ODE3MjIsMTYgMCwxMi40MTgyNzggMCw4IEMwLDMuNTgxNzIyIDMuNTgxNzIyLDAgOCwwIEMxMi40MTgyNzgsMCAxNiwzLjU4MTcyMiAxNiw4IEMxNiwxMi40MTgyNzggMTIuNDE4Mjc4LDE2IDgsMTYgWiBNOCwxNSBDMTEuODY1OTkzMiwxNSAxNSwxMS44NjU5OTMyIDE1LDggQzE1LDQuMTM0MDA2NzUgMTEuODY1OTkzMiwxIDgsMSBDNC4xMzQwMDY3NSwxIDEsNC4xMzQwMDY3NSAxLDggQzEsMTEuODY1OTkzMiA0LjEzNDAwNjc1LDE1IDgsMTUgWiBNNy41LDEyIEw4LjUsMTIgTDguNSwxMyBMNy41LDEzIEw3LjUsMTIgWiIgaWQ9IkNvbWJpbmVkLVNoYXBlIiBmaWxsPSIjMjkzMDQwIiBmaWxsLXJ1bGU9Im5vbnplcm8iPjwvcGF0aD4KICAgICAgICA8L2c+CiAgICA8L2c+Cjwvc3ZnPg=="},"./src/images/search.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTZweCIgaGVpZ2h0PSIxNnB4IiB2aWV3Qm94PSIwIDAgMTYgMTYiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+5pCc57SiPC90aXRsZT4KICAgIDxnIGlkPSLmkJzntKIiIHN0cm9rZT0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIxIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgICAgIDxwYXRoIGQ9Ik03LjcxMjk1NzQyLDEuNTMyODM3OTUgQzExLjAyNjY2NTksMS41MzI4Mzc5NSAxMy43MTI5NTc0LDQuMjE5MTI5NDUgMTMuNzEyOTU3NCw3LjUzMjgzNzk1IEMxMy43MTI5NTc0LDkuMDY5MDAwNiAxMy4xMzU2NjA5LDEwLjQ3MDMyODQgMTIuMTg2MTgzNCwxMS41MzE3MDU5IEwxNC41MzMzMDQxLDEzLjg3ODQ4NzUgQzE0LjcyODU2NjMsMTQuMDczNzQ5NyAxNC43Mjg1NjYzLDE0LjM5MDMzMjEgMTQuNTMzMzA0MSwxNC41ODU1OTQzIEMxNC4zNTk3Mzc4LDE0Ljc1OTE2MDYgMTQuMDkwMzEzNCwxNC43Nzg0NDU4IDEzLjg5NTQ0NTMsMTQuNjQzNDQ5NyBMMTMuODI2MTk3NCwxNC41ODU1OTQzIEwxMS40NjA0NDM0LDEyLjIxODg4MDQgQzEwLjQzMzYzMTksMTMuMDQxMTAyMyA5LjEzMDcyMDE3LDEzLjUzMjgzNzkgNy43MTI5NTc0MiwxMy41MzI4Mzc5IEM0LjM5OTI0ODkzLDEzLjUzMjgzNzkgMS43MTI5NTc0MiwxMC44NDY1NDY0IDEuNzEyOTU3NDIsNy41MzI4Mzc5NSBDMS43MTI5NTc0Miw0LjIxOTEyOTQ1IDQuMzk5MjQ4OTMsMS41MzI4Mzc5NSA3LjcxMjk1NzQyLDEuNTMyODM3OTUgWiBNNy43MTI5NTc0MiwyLjUzMjgzNzk1IEM0Ljk1MTUzMzY4LDIuNTMyODM3OTUgMi43MTI5NTc0Miw0Ljc3MTQxNDIgMi43MTI5NTc0Miw3LjUzMjgzNzk1IEMyLjcxMjk1NzQyLDEwLjI5NDI2MTcgNC45NTE1MzM2OCwxMi41MzI4Mzc5IDcuNzEyOTU3NDIsMTIuNTMyODM3OSBDMTAuNDc0MzgxMiwxMi41MzI4Mzc5IDEyLjcxMjk1NzQsMTAuMjk0MjYxNyAxMi43MTI5NTc0LDcuNTMyODM3OTUgQzEyLjcxMjk1NzQsNC43NzE0MTQyIDEwLjQ3NDM4MTIsMi41MzI4Mzc5NSA3LjcxMjk1NzQyLDIuNTMyODM3OTUgWiIgaWQ9IuW9oueKtue7k+WQiCIgZmlsbD0iIzcxNzU3RiIgZmlsbC1ydWxlPSJub256ZXJvIj48L3BhdGg+CiAgICA8L2c+Cjwvc3ZnPg=="},"./src/images/toggle.svg":function(A){"use strict";A.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS41MzAzIDExLjQ2OTdMMTQuMzUyNiAxMi41MzAzTDEwLjUgOS4wNjA2N0w2LjY0NzM5IDEyLjUzMDNMNS40Njk2NyAxMS40Njk3TDEwLjUgNi45MzkzNUwxNS41MzAzIDExLjQ2OTdaIiBmaWxsPSIjZmZmIi8+Cjwvc3ZnPgo="},"./src/init.js":function(){if(!window.__fr){var A="__fr";window[A]=window[A]||{},window[A].config={appId:"",setting:"perf,jsTrack,api,uba,longtask,rtti",hashMode:!0}}},"./src/js-track.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return s}});t("./src/config.js");var n=t("./src/log.js"),r=t("./src/util.js");function o(A){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},o(A)}function i(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=A.message,t=A.stack,o=r.UTIL.turnToString(e),i=r.UTIL.turnToString(t);if(o){var s=r.UTIL.getConfig().customJsErrorMsg;s&&(o=s(o,i)),n.LOG.report("error",o,{stack:i})}}const s=function(){var A,e;window.addEventListener("error",(function(A){n.LOG.FURION.log("submitCache"),A.error&&i(A.error)}),!0),window.addEventListener("unhandledrejection",(function(A){var e=A.reason,t="",r="";"object"===o(e)&&null!==e&&"message"in e?(t=e.message,r=null==e?void 0:e.stack):t=null==e?void 0:e.toString(),i({message:t="Uncaught (in promise) : ".concat(t),stack:r}),n.LOG.FURION.log("submitCache")})),A=window,e=A.console.error,A.console.error=function(){for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];var s=function(A){for(var e,t="no stack",n=0,r=A.length;n<r;n++)A[n]&&A[n].message&&(e=A[n].message),A[n]&&A[n].stack&&(t=A[n].stack);return{errInfo:e,errStack:t}}(o),a=s.errInfo,c=s.errStack;if(!a){var u=function(A){var e,t,n=A[0];return n&&function(A){return!!A.indexOf&&function(A){return["EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"].some((function(e){return A.includes(e)}))}(A)}(n)&&(e=n,t=n),{errInfo:e,errStack:t}}(o);a=u.errInfo,c=u.errStack}a&&!a.toString().includes("Http failure")&&(a=r.UTIL.turnToString(a.toString().replace(/_=[0-9]{13}/g,"_=ts")),c=r.UTIL.turnToString(c.replace(/_=[0-9]{13}/g,"_=ts")),n.LOG.report("error",a,{stack:c,fs:Math.round(performance.now()),triggerNum:1}),n.LOG.FURION.log("submitCache")),Function.prototype.apply.call(e,A.console,o)}}},"./src/log.js":function(A,e,t){"use strict";t.r(e),t.d(e,{LOG:function(){return x}});var n=t("./node_modules/uuid/dist/esm-browser/v4.js"),r=t("./src/config.js"),o=t("./src/util.js"),i=["sceneId"];function s(A){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},s(A)}function a(A){return function(A){if(Array.isArray(A))return c(A)}(A)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(A)||function(A,e){if(A){if("string"==typeof A)return c(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(A,e):void 0}}(A)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function u(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function l(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?u(Object(t),!0).forEach((function(e){d(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function d(A,e,t){return(e=function(A){var e=function(A,e){if("object"!=s(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var n=t.call(A,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==s(e)?e:e+""}(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}function B(A,e,t){var n=(new Date).getTime(),o=l({t:r.CONFIG.LOG_TYPE[A],ts:n,val:encodeURIComponent(e)},t);return r.CONFIG.GLOBAL_VARS.csidObj[t.sid]&&(o.csid=r.CONFIG.GLOBAL_VARS.csidObj[t.sid]),o}function g(){var A=o.UTIL.getReportUrl();r.CONFIG.GLOBAL_VARS.lastUrl&&r.CONFIG.GLOBAL_VARS.lastUrl!==A&&(Q(r.CONFIG.GLOBAL_VARS.lastUrl),o.UTIL.resetPvid(),r.CONFIG.GLOBAL_VARS.preUrl=r.CONFIG.GLOBAL_VARS.lastUrl,r.CONFIG.GLOBAL_VARS.lastUrl=A,r.CONFIG.GLOBAL_VARS.lastClickInfo=[],C())}function f(A,e){var t=!1,n=!A||!e;if("error"===A&&r.CONFIG.GLOBAL_VARS.smartJsErr){var o=parseInt(sessionStorage.getItem(e)||0);o>r.CONFIG.THRESHOLD.TOO_MUCH_JS_ERR?t=!0:sessionStorage.setItem(e,o+1)}return n||t}function p(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};f(A,e)||("ld"===e||r.CONFIG.GLOBAL_VARS.logList.length>r.CONFIG.THRESHOLD.LOG_INFO?h(A,e,t):(!function(A,e,t){if("click"===A){r.CONFIG.GLOBAL_VARS.lastClickInfo.length>=r.CONFIG.THRESHOLD.CLICK_INFO&&r.CONFIG.GLOBAL_VARS.lastClickInfo.shift();var n=t.xpath,o=t.fx,i=t.tc;r.CONFIG.GLOBAL_VARS.lastClickInfo.push({ts:(new Date).getTime(),fx:o,xpath:n,tc:i})}if("longtask"===A){var s=parseInt(e)||0,c=a(r.CONFIG.GLOBAL_VARS.lastClickInfo).reverse().find((function(A){return A.ts<=(new Date).getTime()-s}))||{},u=c.xpath,l=c.fx,d=c.tc;u&&l&&(t.fx=l,t.xpath=u,t.tc=d)}}(A,e,t),g(),w(A,e,t),clearTimeout(r.CONFIG.GLOBAL_VARS.sendTimer),r.CONFIG.GLOBAL_VARS.sendTimer=setTimeout((function(){Q(o.UTIL.getReportUrl())}),2e3)))}function h(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!f(A,e)){var n=o.UTIL.getReportUrl();if(g(),t.FurionAppId){var r=B(A,e,t),i=null==r?void 0:r.FurionAppId;return i&&delete r.FurionAppId,void y(n,[r],i)}w(A,e,t),Q(n)}}function w(A,e,t){var n=B(A,e,t);r.CONFIG.GLOBAL_VARS.logList.push(n)}var C=function(){r.CONFIG.GLOBAL_VARS.cacheList=[]};function Q(A){var e,t,n,i=r.CONFIG.GLOBAL_VARS.logList;r.CONFIG.GLOBAL_VARS.logList=function(A){var e=["api","er","lt","res","static"],t=window.performance.timing,n=parseInt(t.loadEventEnd-t.fetchStart,10),r=JSON.parse(JSON.stringify(A));return r.filter((function(A){return e.indexOf(A.t)>-1})).forEach((function(A){var e=t.fetchStart-t.navigationStart;A.fs=o.UTIL.getPositiveInt(A.fs-e);var r=n<0||A.fs<n;A.bl=r?1:0})),r}(i),n={},r.CONFIG.GLOBAL_VARS.logList.forEach((function(A,e){var t=A.t,r=A.val;"er"===t&&(n[r]?n[r].push(e):n[r]=[e])})),e=n,t="--delete--",Object.keys(e).forEach((function(A){var n=e[A],o=n[0];r.CONFIG.GLOBAL_VARS.logList[o].triggerNum=n.length;for(var i=1;i<n.length;i++){var s=n[i];r.CONFIG.GLOBAL_VARS.logList[s].t=t}})),r.CONFIG.GLOBAL_VARS.logList=r.CONFIG.GLOBAL_VARS.logList.filter((function(A){return A.t!==t})),y(A,r.CONFIG.GLOBAL_VARS.logList),r.CONFIG.GLOBAL_VARS.logList=[]}var m=function(A){var e,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;return function(){for(var n=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];e&&clearTimeout(e),e=setTimeout((function(){A.apply(n,o)}),t)}}((function(A){"complete"===document.readyState&&p("page",A,{tt:o.UTIL.cutTooLongStr(document.title)})}),250);function U(){var A,e;return A=!1,(e=(new Date).getDate())!==r.CONFIG.GLOBAL_VARS.lastReportDate&&(A=!0),r.CONFIG.GLOBAL_VARS.lastReportDate=e,(A||!o.UTIL.getCookie(r.CONFIG.COOKIES.ssid))&&(o.UTIL.resetSsid(),o.UTIL.resetPvid(),m("ps")),r.CONFIG.GLOBAL_VARS.lastPvid===r.CONFIG.VISIT_INFO.pvid&&r.CONFIG.GLOBAL_VARS.lastSsid!==o.UTIL.getCookie(r.CONFIG.COOKIES.ssid)&&o.UTIL.resetPvid(),{ssid:o.UTIL.getCookie(r.CONFIG.COOKIES.ssid),pvid:r.CONFIG.VISIT_INFO.pvid}}function v(A){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=U(),n=t.ssid,i=t.pvid,s=r.CONFIG.VISIT_INFO,a=s.pid,c=s.browser,u=s.os,l={appId:e||o.UTIL.getAppId(A),pid:a,pvid:i,uid:o.UTIL.getUserId(),uuid:r.CONFIG.GLOBAL_VARS.randomId,userInfo:o.UTIL.getEncodedUserInfo(),url:A,brt:c.split(": ")[0],brv:c.split(": ")[1],os:u,sr:"".concat(window.screen.width,"*").concat(window.screen.height),v:r.CONFIG.GLOBAL_VARS.VERSION,ssid:n,effTy:navigator.connection?navigator.connection.effectiveType:"",eno:F(r.CONFIG.GLOBAL_VARS.eno),bucket:o.UTIL.getABtestParams()};return Object.keys(l).filter((function(A){return null!==l[A]})).map((function(A){return"".concat(A,"=").concat(l[A])})).join("&")}function y(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=r.CONFIG.VISIT_INFO.browser;if(o.UTIL.needReport()&&e.length&&n){var i=v(A,t),s="/fr?".concat(i);o.UTIL.httpRequest("".concat(r.CONFIG.GLOBAL_VARS.domain).concat(s),"post",e).catch((function(A){})),o.UTIL.extendSsid()}}var F=function(A){var e=A;return/^[a-zA-Z0-9-]{5,50}$/.test(A)||(e=null),e},I={start:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};A&&(r.CONFIG.GLOBAL_VARS.storeObj[A]=(new Date).getTime()),Object.keys(e).forEach((function(A){r.CONFIG.GLOBAL_VARS.storeObj[A]=e[A]}))},end:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=(new Date).getTime();A&&r.CONFIG.GLOBAL_VARS.storeObj[A]&&(h("operation",A,l({dr:t-r.CONFIG.GLOBAL_VARS.storeObj[A]},e)),delete r.CONFIG.GLOBAL_VARS.storeObj[A])}},b=function(A,e,t){var o=t.sceneId,s=function(A,e){if(null==A)return{};var t,n,r=function(A,e){if(null==A)return{};var t={};for(var n in A)if({}.hasOwnProperty.call(A,n)){if(-1!==e.indexOf(n))continue;t[n]=A[n]}return t}(A,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(A);for(n=0;n<o.length;n++)t=o[n],-1===e.indexOf(t)&&{}.propertyIsEnumerable.call(A,t)&&(r[t]=A[t])}return r}(t,i);!["start","end"].includes(A)||o?"custom"!==A||o&&1!==o?o&&"string"==typeof o&&function(A,e,t,o){"start"===A&&(r.CONFIG.GLOBAL_VARS.csidObj[t]=(0,n.default)().split("-").join("")),h(A,e,l({sid:t},o)),"end"===A&&delete r.CONFIG.GLOBAL_VARS.csidObj[t]}(A,e,o,s):h("operation",e,s):I[A](e,s)},E=function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if("[object Object]"===Object.prototype.toString.call(n)){if(["info","event"].includes(A)&&("[object Object]"===Object.prototype.toString.call(t)&&(t=JSON.stringify(t)),t&&(n.info=t),h("operation",e,n)),["start","custom","end"].includes(A)&&(t&&"string"==typeof t&&(n.sceneId=t),b(A,e,n)),"cache"===A){var i=(n||{}).maxCaches,s=void 0===i?100:i;t&&(t=JSON.stringify(t),n.info=t),function(A,e,t){var n="number"==typeof t?t:100;if(!(n<=0)){var o=B("operation",A,e),i=r.CONFIG.GLOBAL_VARS.cacheList.concat(o);r.CONFIG.GLOBAL_VARS.cacheList=i.length>n?i.slice(-n):i}}(e,n,s)}"submitCache"===A&&(y(o.UTIL.getReportUrl(),r.CONFIG.GLOBAL_VARS.cacheList),C())}},L={now:function(){return(new Date).getTime()},log:function(){E(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",arguments.length>3&&void 0!==arguments[3]?arguments[3]:{})},log2:function(){E(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",arguments.length>1&&void 0!==arguments[1]?arguments[1]:"","",arguments.length>2&&void 0!==arguments[2]?arguments[2]:{})},logComponent:function(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(A&&e&&(!1!==n||!1!==r.CONFIG.GLOBAL_VARS.checkComponentConfig)){var i="";try{i=o.UTIL.safeStringify(e)}catch(e){p("error","id为".concat(A,"的Console组件配置信息解析失败，失败原因：").concat(e.message))}p("componentConfig","cc",{cc:{id:A,configDetails:i,type:t,reportAnyTime:n}})}},start:function(){},end:function(){}},x={FURION:L,report:p,handlerPvReport:m,getSsidAndPvid:U,getValidEno:F,getQueryStr:v}},"./src/longtask.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return i}});var n=t("./src/log.js");function r(A,e){var t="undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(!t){if(Array.isArray(A)||(t=function(A,e){if(A){if("string"==typeof A)return o(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?o(A,e):void 0}}(A))||e&&A&&"number"==typeof A.length){t&&(A=t);var n=0,r=function(){};return{s:r,n:function(){return n>=A.length?{done:!0}:{done:!1,value:A[n++]}},e:function(A){throw A},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,a=!1;return{s:function(){t=t.call(A)},n:function(){var A=t.next();return s=A.done,A},e:function(A){a=!0,i=A},f:function(){try{s||null==t.return||t.return()}finally{if(a)throw i}}}}function o(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}const i=function(){try{new PerformanceObserver((function(A){var e,t=r(A.getEntries());try{for(t.s();!(e=t.n()).done;){var o=e.value,i=o.startTime,s=o.duration;Math.round(s)>300&&n.LOG.report("longtask",Math.round(s),{fs:Math.round(i)})}}catch(A){t.e(A)}finally{t.f()}})).observe({entryTypes:["longtask"]})}catch(A){}}},"./src/module.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return a}});var n,r=t("./src/domActions.js");function o(A){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},o(A)}function i(A,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(A,s(n.key),n)}}function s(A){var e=function(A,e){if("object"!=o(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var n=t.call(A,e||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==o(e)?e:e+""}const a=((n=null)||(n=new(function(){return A=function A(){!function(A,e){if(!(A instanceof e))throw new TypeError("Cannot call a class as a function")}(this,A),this.mask=r.DOMACTIONS.createNode("div"),this.setStyle(this.mask,{width:"100%",height:"100%",backgroundColor:"rgba(0, 0, 0, .2)",position:"absolute",left:0,top:0,zIndex:1e5}),this.content=r.DOMACTIONS.createNode("div"),this.setStyle(this.content,{width:"400px",height:"145px",backgroundColor:"#fff",boxShadow:"0 12px 24px 0 rgba(37,43,58,.2)",position:"absolute",left:"50%",top:"50%",transform:"translate(-50%,-50%)",borderRadius:"6px"}),this.mask.appendChild(this.content)},(e=[{key:"middleBox",value:function(A){this.content.innerHTML="";var e="默认标题内容",t=r.DOMACTIONS.createNode("div");"[object String]"==={}.toString.call(A)?t.innerHTML=A:"[object Object]"==={}.toString.call(A)&&(e=A.title,t.innerHTML=A.content),document.body.appendChild(this.mask),this.title=r.DOMACTIONS.createNode("div"),this.setStyle(this.title,{width:"100%",height:"50px",fontSize:"18px",fontWeight:"700",color:"#252b3a",paddingLeft:"20px",boxSizing:"border-box",lineHeight:"50px"}),this.title.innerText=e,this.content.appendChild(this.title),this.closeBtn=r.DOMACTIONS.createNode("a"),this.closeBtn.innerText="×",this.closeBtn.setAttribute("href","javascript:;"),this.setStyle(this.closeBtn,{textDecoration:"none",color:"#666",position:"absolute",right:"10px",top:"6px",fontSize:"25px"}),this.content.appendChild(this.closeBtn),this.description=r.DOMACTIONS.createNode("div"),this.description.appendChild(t),this.content.appendChild(this.description),this.setStyle(this.description,{color:"#575d6c",paddingLeft:"20px",fontSize:"12px"})}},{key:"alert",value:function(A){var e=this;this.middleBox(A),this.btn=r.DOMACTIONS.createNode("button"),this.btn.innerText="确定",this.setStyle(this.btn,{backgroundColor:"#5e7ce0",position:"absolute",right:"10px",bottom:"20px",outline:"none",border:"none",color:"#fff",fontSize:"14px",borderRadius:"2px",padding:"0 10px",height:"30px",lineHeight:"30px"}),this.content.appendChild(this.btn),this.btn.onclick=this.closeBtn.onclick=function(){return e.close()}}},{key:"confirm",value:function(A,e,t,n){var o=this;this.middleBox(A);var i={btn:["确定","取消"]},s=function(){},a=function(){};if(e&&"[object Object]"==={}.toString.call(e))for(var c in e)Object.prototype.hasOwnProperty.call(e,c)&&(i[c]=e[c]);t&&"[object Function]"==={}.toString.call(t)&&(s=t),n&&"[object Function]"==={}.toString.call(n)&&(a=n),this.btn=r.DOMACTIONS.createNode("button"),this.btn.innerText=i.btn[0],this.setStyle(this.btn,{backgroundColor:"#5e7ce0",position:"absolute",right:"145px",bottom:"20px",outline:"none",border:"none",color:"#fff",fontSize:"14px",borderRadius:"2px",padding:"0 10px",height:"30px",lineHeight:"30px"}),this.content.appendChild(this.btn),this.cancel=r.DOMACTIONS.createNode("button"),this.cancel.innerText=i.btn[1],this.setStyle(this.cancel,{backgroundColor:"transparent",position:"absolute",left:"145px",bottom:"20px",outline:"none",border:"1px solid #ccc",color:"#666",fontSize:"14px",borderRadius:"2px",padding:"0 10px",height:"30px",lineHeight:"30px"}),this.content.appendChild(this.cancel),this.closeBtn.onclick=function(){return o.close()},this.btn.onclick=function(){s(),o.close()},this.cancel.onclick=function(){a(),o.close()}}},{key:"msg",value:function(A,e){var t=this;this.content.innerHTML="";var n="默认提示内容",o={time:2e3};if(A&&"[object String]"==={}.toString.call(A)&&(n=A),e&&"[object Object]"==={}.toString.call(e))for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(o[i]=e[i]);document.body.appendChild(this.mask),this.setStyle(this.content,{width:"auto",height:"80px",backgroundColor:"rgb(255, 255, 255)",boxShadow:"0 0 2px #999",position:"absolute",left:"50%",top:"50%",transform:"translate(-50%,-50%)",borderRadius:"3px",padding:"0 30px 0",lineHeight:"80px",color:"#666"});var s=r.DOMACTIONS.createNode("span");1===o.icon?(s.innerText="√",this.setStyle(s,{width:"30px",height:"30px",border:"3px solid rgb(56, 201, 177)",borderRadius:"50%",display:"inline-block",color:"rgb(56, 201, 177)",lineHeight:"30px",textAlign:"center",margin:"0 10px",fontSize:"20px",fontWeight:"bold"})):0===o.icon?(s.innerText="×",this.setStyle(s,{width:"30px",height:"30px",border:"3px solid rgb(233, 91, 76)",borderRadius:"50%",display:"inline-block",color:"rgb(233, 91, 76)",lineHeight:"30px",textAlign:"center",margin:"0 10px",fontSize:"20px",fontWeight:"bold"})):this.setStyle(this.content,{width:"auto",height:"50px",backgroundColor:"rgba(0, 0, 0, 0.5)",boxShadow:"0 0 2px #999",position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",borderRadius:"3px",padding:"0 30px 0",lineHeight:"50px",color:"#fff"}),this.content.appendChild(s),this.content.innerHTML+=n,setTimeout((function(){t.mask.parentElement&&document.body.removeChild(t.mask)}),o.time)}},{key:"load",value:function(){var A=this;document.body.appendChild(this.mask),this.setStyle(this.content,{border:"none",boxShadow:"0 0 0 transparent",backgroundColor:"transparent"});for(var e=0;e<8;e++){var t=r.DOMACTIONS.createNode("div");this.setStyle(t,{width:"4px",height:"12px",backgroundColor:"rgb(6, 43, 78)",transformOrigin:"0 15px",transform:"rotate("+45*e+"deg) translate(-50%, -50%)",position:"absolute",left:"50%",top:"50%",opacity:"0.5"}),0===e&&(t.style.opacity=1),this.content.appendChild(t)}var n=0;return setInterval((function(){++n===A.content.children.length?(n=0,A.content.children[A.content.children.length-1].style.opacity="0.5"):A.content.children[n-1].style.opacity="0.5",A.content.children[n].style.opacity=1}),50)}},{key:"close",value:function(A){A&&clearInterval(A),document.body.removeChild(this.mask)}},{key:"setStyle",value:function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A.style[t]=e[t])}}])&&i(A.prototype,e),t&&i(A,t),Object.defineProperty(A,"prototype",{writable:!1}),A;var A,e,t}())),n)},"./src/newNav.js":function(A,e,t){"use strict";t.r(e),t.d(e,{NEWNAV:function(){return Z},default:function(){return Y}});var n=t("./src/util.js"),r=t("./src/module.js"),o=t("./src/newToast.js"),i=t("./src/service.js"),s=t("./src/codeLess.js"),a=t("./src/domActions.js"),c=t("./src/config.js"),u=t("./src/heatMap.js"),l=t("./src/codeLessConfig.js"),d=t("./src/consistency.js");function B(A,e){var t="undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(!t){if(Array.isArray(A)||(t=function(A,e){if(A){if("string"==typeof A)return g(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?g(A,e):void 0}}(A))||e&&A&&"number"==typeof A.length){t&&(A=t);var n=0,r=function(){};return{s:r,n:function(){return n>=A.length?{done:!0}:{done:!1,value:A[n++]}},e:function(A){throw A},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,s=!1;return{s:function(){t=t.call(A)},n:function(){var A=t.next();return i=A.done,A},e:function(A){s=!0,o=A},f:function(){try{i||null==t.return||t.return()}finally{if(s)throw o}}}}function g(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}var f=[t("./src/images/back-arrow.svg"),t("./src/images/events.svg"),t("./src/images/toggle.svg"),t("./src/images/close.svg"),t("./src/images/search.svg"),t("./src/images/filter.svg"),t("./src/images/filter-active.svg")],p="event",h="heatmap",w="consistency",C="browsing",Q=p,m=!1,U=null,v=null,y=null,F=null,I=null,b=[{id:"boxShadow",name:"元素阴影"},{id:"padding",name:"内间距"},{id:"fontSize",name:"字体大小"},{id:"color",name:"字体颜色"},{id:"borderRadius",name:"圆角"},{id:"tableSpecification",name:"组件规范"}],E={offline:"offline",realTime:"realTime"},L=[{id:E.offline,name:"离线"},{id:E.realTime,name:"实时"}],x=E.offline;function H(A){var e=new Date(A),t=e.getFullYear().toString(),n=(e.getMonth()+1).toString().padStart(2,"0"),r=e.getDate().toString().padStart(2,"0");return"".concat(t,"-").concat(n,"-").concat(r)}function S(){U.querySelector("#dom_based_heatmap_container").style.height="0",U.querySelector(".heatmap-canvas").style.height="0";var A=U.querySelector(".furion-hover-dom-container");A&&A.remove(),T()}function T(){n.UTIL.forbidClick(),F&&clearTimeout(F),F=setTimeout(u.Heatmap.runHeatmap,1e3)}function O(){I&&clearTimeout(I),I=setTimeout(d.Consistency.runConsistency.bind(this,x),1e3)}function M(){window.removeEventListener("resize",O),window.removeEventListener("scroll",O,!0)}function D(){window.removeEventListener("resize",T),window.removeEventListener("scroll",T,!0),window.removeEventListener("wheel",S,!0)}function j(){var A=a.DOMACTIONS.createNode("div","furion-navbar-consistency-select-box"),e=a.DOMACTIONS.createNode("select","","consistencySelect","","","","",22);return new Promise((function(A,e){var t=n.UTIL.getConfig().searchDomain;n.UTIL.httpRequest("".concat(t,"/style/specification/type"),"get",{}).then((function(e){A(null==e?void 0:e.result)}))})).then((function(A){(b=(null==A?void 0:A.categories)||b).forEach((function(A){var t=a.DOMACTIONS.createNode("option","","",A.name);t.value=A.id,e.appendChild(t)}))})),A.appendChild(e),A}function N(A){U.querySelector("#furion-event-switch").style.display=A,U.querySelector("#furion-events").style.display=A}function K(A){U.querySelector(".furion-navbar-time-box").style.display=A}function _(A){U.querySelector(".query-data-btn").style.display=A}function k(A){U.querySelector(".furion-navbar-consistency-input-box").style.display=A,U.querySelector(".furion-navbar-consistency-select-box").style.display=A,U.querySelector(".furion-navbar-consistency-mode-select-box").style.display=A}function G(A){var e=a.DOMACTIONS.createNode("li","furion-events-item");if(A.eventName){var t=A.fx,n=A.eventName,r=A.eventId,o=A.fuzzys,i=A.formattedUrl,s=A.tc,c=void 0===s?"":s,u=A.ecls,l=void 0===u?"":u,d=A.matchPageType,B=void 0===d?"":d,g=A.matchPageUrl,f=void 0===g?"":g,p=A.eid,h=void 0===p?"":p,w=A.noPointEventType,C=void 0===w?0:w;o=JSON.stringify(o),e.innerHTML+="<div class=\"furion-events-title\" fr_atr='furion-node' fr_matchPageType='".concat(B,"' fr_matchPageUrl='").concat(f,"' fr_fx='").concat(t,"' fr_fuzzys='").concat(o,"' fr_eventName='").concat(n,"' fr_eventId='").concat(r,"' fr_url='").concat(i,"' fr_tc='").concat(c,"' fr_ecls='").concat(l,"' fr_eid=").concat(h," fr_noPointEventType=").concat(C,">").concat(n,"</div><div class=\"furion-events-del\" fr_atr='furion-node' id=").concat(r," name=").concat(n," >删除</div>")}return e}function P(A){var e=U.querySelector(".function-dom-hover"),t=A.target;if(t&&t.parentElement&&!t.getAttribute("fr_atr"))if(e){var n=a.DOMACTIONS.getBoundingClientRect(t),r=n.width,o=n.height,i=n.left,s=n.top;e.setAttribute("style","display:block;width:".concat(r,"px;height:").concat(o,"px;left:").concat(i,"px;top:").concat(s,"px"))}else{var c=a.DOMACTIONS.createActive(t,"hover");U.appendChild(c)}else e&&e.setAttribute("style","display:none")}function R(A,e,t,n,r,o,i,a,c,u,l,d,B){if("new"===t)for(var g=U.querySelectorAll(".furion-events-item"),f=0;f<g.length;f++)g[f].classList.remove("furion-events-item-active");s.Codeless.showShadow(A,e,t,n,r,o,i,a,c,u,l,d,B)}function V(A){var e;(e=A.target)&&e.parentElement&&!e.getAttribute("fr_atr")&&(A.stopPropagation(),A.preventDefault(),a.DOMACTIONS.getPeersDom(n.UTIL.travelWithFunc(A.target,n.UTIL.fxFun),-1,"active"),R(A.target,"click","new"))}var z=[];function X(A){var e=("undefined"===A||"null"===A?[]:JSON.parse(A)).map((function(A,e){return 1===A?e:-1})).filter((function(A){return-1!==A}));return e.length?e:-1}function J(){a.DOMACTIONS.removeMarkedDom(),y&&clearTimeout(y),y=setTimeout((function(){i.API.getEventList().then((function(A){a.DOMACTIONS.showAllMarkedDom(A||[])}))}),1e3)}function W(){var A=U.querySelector("#dom_based_heatmap_container");if(A){u.Heatmap.clearHeatMap();var e=A.querySelector(".heatmap-canvas");e&&(e.style.width="0px",e.style.height="0px");var t=A.querySelector(".furion-hover-dom-container");t&&t.remove(),A.style.width="0px",A.style.height="0px"}}const Y=function(){o.TOAST.newToast()};var Z={createNav:function(){U=document.querySelector(".code-less-box").shadowRoot;var A=c.CONFIG.IS_IFRAME?"furion-navbar-box furion-iframe-navbar-box":"furion-navbar-box",e=a.DOMACTIONS.createNode("div",A),t=a.DOMACTIONS.createNode("ul","furion-navbar"),n=a.DOMACTIONS.createNode("div","furion-navbar-left"),r=a.DOMACTIONS.createNode("li","navbar-item back-arrow","furion-back");r.appendChild(a.DOMACTIONS.createNode("img","navbar-item-icon","","","",f[0])),r.appendChild(a.DOMACTIONS.createNode("span","navbar-item-text","","返回Furion")),n.appendChild(r);var s=a.DOMACTIONS.createNode("li","navbar-item events","furion-events");s.appendChild(a.DOMACTIONS.createNode("img","navbar-item-icon","","","",f[1])),s.appendChild(a.DOMACTIONS.createNode("span","navbar-item-text","","已标记事件")),n.appendChild(s);var l=a.DOMACTIONS.createNode("li","navbar-item event-switch","furion-event-switch"),B=a.DOMACTIONS.createNode("span","navbar-item-text","","展示标记元素"),g=a.DOMACTIONS.createNode("div","switch-box"),p=a.DOMACTIONS.createNode("input","switch","switch");p.setAttribute("type","checkbox"),g.appendChild(p),g.appendChild(a.DOMACTIONS.createNode("label","switch-label")),l.appendChild(B),l.appendChild(g),n.appendChild(l),t.appendChild(n),g.addEventListener("click",(function(){v&&clearTimeout(v),v=setTimeout((function(){p.checked=!p.checked,g.style.background=p.checked?"#5e7ce0":"#ccc",p.checked?(i.API.getEventList().then((function(A){a.DOMACTIONS.showAllMarkedDom(A||[])})),window.addEventListener("scroll",J)):(a.DOMACTIONS.removeMarkedDom(),window.removeEventListener("scroll",J))}),200)}));var C=a.DOMACTIONS.createNode("div","furion-navbar-right"),m=a.DOMACTIONS.createNode("ul","navbar-tab");m.appendChild(a.DOMACTIONS.createNode("li","navbar-tab-item navbar-tab-item-active navbar-tab-item-left","furion-point","定义事件")),m.appendChild(a.DOMACTIONS.createNode("li","navbar-tab-item navbar-tab-item-heat-map","furion-heat-map","热力模式")),m.appendChild(a.DOMACTIONS.createNode("li","navbar-tab-item navbar-tab-item-consistency","furion-consistency","一致性检测")),m.appendChild(a.DOMACTIONS.createNode("li","navbar-tab-item navbar-tab-item-right","furion-browsing","浏览网页")),C.appendChild(m),t.appendChild(C);var y=a.DOMACTIONS.createNode("div","toggle-btn","furion-nav-toggle");y.appendChild(a.DOMACTIONS.createNode("img","furion-toggle-btn-icon","","","",f[2])),t.appendChild(y);var F,I,b=(F=a.DOMACTIONS.createNode("div","furion-navbar-consistency-input-box"),(I=a.DOMACTIONS.createNode("input","consistency-input","","","","","",18)).setAttribute("type","text"),I.setAttribute("placeholder","请输入xpath"),F.appendChild(I),F);t.appendChild(b);var S,T,O=(S=a.DOMACTIONS.createNode("div","furion-navbar-consistency-mode-select-box"),T=a.DOMACTIONS.createNode("select","","consistencyModeSelect","","","","",22),L.forEach((function(A){var e=a.DOMACTIONS.createNode("option","","",A.name);e.value=A.id,T.appendChild(e)})),T.onchange=function(A){x=T.value,T.value===E.offline?(K("flex"),U.querySelector(".furion-navbar-box").style.width="1300px"):T.value===E.realTime&&(K("none"),U.querySelector(".furion-navbar-box").style.width="1070px")},S.appendChild(T),S);t.appendChild(O);var M=j();t.appendChild(M);var D=function(){var A=a.DOMACTIONS.createNode("div","furion-navbar-time-box"),e=(new Date).getTime(),t=e-864e5,n=H(e-316224e5),r=H(t),i=a.DOMACTIONS.createNode("input","start-time");i.type="date",i.min=n,i.max=r,i.value=H(t),i.addEventListener("blur",(function(){i.value<n?(i.value=n,o.TOAST.toast("最多可查看近一年的数据",3e3)):i.value>c.value&&(i.value=c.value,o.TOAST.toast("开始时间不能大于结束时间",3e3)),c.min=i.value})),A.appendChild(i);var s=a.DOMACTIONS.createNode("span","wavy-line","","至");A.appendChild(s);var c=a.DOMACTIONS.createNode("input","end-time");return c.type="date",c.min=i.value,c.max=r,c.value=H(t),c.addEventListener("blur",(function(){c.value>r?(c.value=r,o.TOAST.toast("最多可查看截至到昨天的数据",3e3)):c.value<i.value&&(c.value=i.value,o.TOAST.toast("结束时间不能小于开始时间",3e3)),i.max=c.value})),A.appendChild(c),A}();t.appendChild(D);var N,_=((N=a.DOMACTIONS.createNode("button","query-data-btn","","查询")).addEventListener("click",(function(){Q===h?u.Heatmap.runHeatmap():Q===w&&d.Consistency.runConsistency(x)})),N);t.appendChild(_),e.appendChild(t),a.DOMACTIONS.addCodeLessCSS(".furion-navbar-box{position:fixed;top: 0;width:1000px;z-index:999999;left: 50%;margin-left:-600px;}.furion-iframe-navbar-box{top:100px;}.furion-iframe-navbar-box .furion-navbar{ border-radius:10px; background:rgba(0,0,0,0.7)}.furion-navbar{align-items: center;display:flex;justify-content: space-between;background:#000;color:#fff;margin:0;height:50px;border-radius:0 0 8px 8px;padding:0 20px;transition:all 0.5s;}.furion-navbar-left{display:flex;}.furion-navbar .navbar-item{display:flex;align-items: center;list-style:none;padding:13px 15px;cursor:pointer;font-size:14px;}.furion-navbar .navbar-item:hover{background:linear-gradient(to bottom,#333,#222)}.furion-navbar .navbar-item .navbar-item-icon{margin-right:8px;}\n         .furion-popover {display: inline-block; position:relative;}.furion-popover-content { z-index: 100000;margin-top: 5px;position: absolute; width: 200px; padding: 10px; background: #464d6e;border-radius: 4px; color: #dfe1e6; font-size: 14px; left: -94px; box-shadow: 0 4px 16px #252b3a3d; display: none;}.furion-popover-content:before {width: 10px; height: 10px;display: block;content: \"\"; background: #464d6e;position: absolute; top: -5px; left: 100px; transform: rotate(45deg);}.furion-popover-item:hover + .furion-popover-content {display: block;}\n         #furion-events{position:relative;}.furion-eventsList{width:260px;position:absolute;top:49px;left:0;border:1px solid rgb(230, 233, 242); border-radius: 4px; background-color: rgb(241, 244, 248); box-shadow: 0px 3px 20px 0px rgba(64, 98, 255, 0.2);display:none;height:310px;padding:4px 10px;}.furion-eventsList::-webkit-scrollbar{width:4px;}.furion-eventsList::-webkit-scrollbar-thumb {border-radius: 10px;-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);.furion-eventsList::-webkit-scrollbar-track {-webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);border-radius: 0;background: fade(@primary-color, 30%);}opacity: 0.2;background: fade(@primary-color, 60%);}.furion-eventsUl{padding:0;height:250px;overflow-y:auto;overflow-x:hidden;} .search-wrap{width:80%; margin:10px; line-height:25px; display:inline-block; position:relative;}.search-icons{position:absolute;top:5px;right:5px;}.furion-event-filter{vertical-align: middle;}#furion-search-input{box-sizing: border-box;width: 100%; height: 25px; font-size: 12px; border: 1px solid #adb0b8;border-radius: 4px; margin: 2px 0;text-indent: 5px;}#furion-search-input:hover{border-color: #71757f}#furion-search-input:focus{border-color: #252b3a}#furion-search-input:focus-visible{border-color: #252b3a; outline: none}#furion-clear-icon{opacity: 0}.furion-events-item{display:flex; justify-content: space-between;padding:0 10px;margin:2px 0;overflow:hidden;text-overflow:ellipsis;color:#333;font-size:12px;height:25px;line-height:25px;border-radius:4px;box-sizing: content-box;}.furion-events-item:hover{background-color: #5e7ce0;color:#fff;}.furion-events-title{width:190px;overflow:hidden;text-overflow:ellipsis;}.furion-events-item-active{background:#5e7ce0;color:#fff}.furion-events-del{opacity:0;width:30px;text-align:right;}.furion-events-item:hover>.furion-events-del{opacity:1;}.furion-events-del:hover{text-decoration:underline;}.furion-checkbox-div{color:#333;display:flex;align-items:center;}.furion-checkbox-div input{margin-right:5px;cursor: pointer;}.furion-pl-10{padding-left:10px}.furion-mt-5{margin-top:5px;}.furion-events-search .furion-popover-content{text-align:center;}\n         .furion-navbar-right .navbar-tab{display:flex;cursor:pointer;}.furion-navbar-right .navbar-tab .navbar-tab-item{list-style:none;font-size:14px;padding:6px 7px;width:100px;text-align:center;background-color:#5f6663;color:#fff;}.furion-navbar-right .navbar-tab .navbar-tab-item-left{border-radius:4px 0 0 4px;}.furion-navbar-right .navbar-tab .navbar-tab-item-right{border-radius:0 4px 4px 0;}.navbar-tab-item-heat-map,.navbar-tab-item-consistency{border-left:1px solid #e2e6e5;border-right:1px solid #e2e6e5} .furion-navbar-right .navbar-tab .navbar-tab-item-active{background-color:#e2e6e5;color:#000;}\n         @keyframes rotating {from{transform: rotateY(0deg);}to{transform: rotateY(180deg);}} .furion-navbar-time-box{width:230px;display:none;justify-content: flex-start;align-items:center;} .wavy-line{padding:0 10px} .query-data-btn{margin-left: 10px}\n         .furion-navbar .toggle-btn{position:absolute;width:24px;height:20px;left:50%;margin-left:-12px;bottom:-14px;background-color:#343036;z-index:10000;border-radius:0 0 12px 12px;text-align:center;cursor:pointer;}.furion-navbar-close{height:0;overflow:hidden;}.furion-arrow-icon{transform:rotate(180deg);transition:all 0.5s;}\n         .switch-box {display: inline-block;background: #ccc; border-radius: 32px;width: 32px;position: relative;top:1px;left:4px;height: 16px;cursor: pointer;}\n        .switch-label {display: block;width: 16px;height: 16px;border-radius: 50%;background: #fff;border: 1px solid #e5e5e5;position: absolute;top: 0px;left: 0px;box-sizing: border-box;cursor: pointer;}\n        input[type='checkbox'].switch {border: 0 none !important;clip: rect(1px, 1px, 1px, 1px);height: 1px !important;overflow: hidden !important;position: absolute !important;width: 1px !important;}\n        @keyframes switchON {0% {top: 0px;left: 0px;} 100% {top: 0px;left: 16px;}}\n        @keyframes switchOFF {0% {top: 0px;left: 16px;} 100% {top: 0px;left: 0px;}}\n        input[type='checkbox'].switch:checked + label.switch-label {top: 0px;left: 16px;-webkit-animation: switchON 0.2s ease-in 0s 1;-moz-animation: switchON 0.2s ease-in 0s 1;-o-animation: switchON 0.2s ease-in 0s 1;-ms-animation: switchON 0.2s ease-in 0s 1;animation: switchON 0.2s ease-in 0s 1;box-shadow: #244766 -1px 0px 3px;}\n        input[type='checkbox'].switch + label.switch-label {top: 0px;left: 0px;-webkit-animation: switchOFF 0.2s ease-in 0s 1;-moz-animation: switchOFF 0.2s ease-in 0s 1;-o-animation: switchOFF 0.2s ease-in 0s 1;-ms-animation: switchOFF 0.2s ease-in 0s 1;animation: switchOFF 0.2s ease-in 0s 1;box-shadow: #244766 1px 0px 3px;}\n        #dom_based_heatmap_container {position: fixed !important;top:0;left:0;z-index:99999;}\n        #furion-loading { border: 8px solid #f3f3f3;border-top: 8px solid #3498db; border-radius: 50%; width: 60px; height: 60px; animation: spin 2s linear infinite; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); display: none; }\n        @keyframes spin {0% { transform: translate(-50%, -50%) rotate(0deg); }  100% { transform: translate(-50%, -50%) rotate(360deg); }}\n        .heatmap-hover-box {position:absolute;opacity: 0;}  \n        .furion-hover-border-dom {border:1px dashed red;opacity: 0} \n        .furion-hover-info-dom {position:relative;display:none;background-color:#464d6e;border-radius:5px;color:#dfe1e6;padding:4px 15px;box-sizing:border-box;} \n        .heatmap-hover-box:hover {opacity: 1;} .heatmap-hover-box:hover .furion-hover-border-dom {opacity: 1;} .heatmap-hover-box:hover .furion-hover-info-dom {display:block;}\n        .heatmap-triangle {border:4px solid transparent;border-bottom-color:#464d6e;position:absolute;}\n        .info-connect-dom {position:absolute;}\n        .info-content-dom {max-width:370px;margin:16px 0;max-height:60px;overflow-y:auto;}\n        .unclickable {pointer-events: none;}\n        .furion-consistency-dom {position:fixed;border:1px solid #ff9f00eb;background-color:rgba(232,130,29,.1);z-index:99999}\n        .furion-consistency-dom:hover {border-width:2px}\n        .furion-navbar-consistency-input-box, .furion-navbar-consistency-select-box,.furion-navbar-consistency-mode-select-box {display:none;}\n        .custom-confirm-box {width:400px;height:145px;border-radius:6px; position: fixed;left: 50%;top: 50%;transform: translate(-50%, -50%);padding: 20px;background-color: rgb(241,244,248);border: 1px solid #ccc;box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);z-index: 9999999;}\n        .custom-confirm-box-overlay {position: fixed;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.5);z-index: 999999;}\n        .custom-confirm-title {text-align:center;font-size:20px;}\n        .custom-confirm-box button {width:50px;margin:35px 0 0 100px;padding:6px 12px;}\n        "),U.appendChild(e);var k=a.DOMACTIONS.createNode("div","","dom_based_heatmap_container","","","","","",0,0),G=a.DOMACTIONS.createNode("div","","furion-loading");U.appendChild(G),U.appendChild(k),window.addEventListener("mouseover",P,!0),window.addEventListener("click",V,!0)},onloadNavEvent:function(){var A=U.querySelector(".furion-navbar"),e=U.querySelector("#furion-events"),t=a.DOMACTIONS.createNode("div","furion-eventsList"),g=a.DOMACTIONS.createNode("ul","furion-eventsUl"),v=U.querySelector("#furion-point"),y=U.querySelector("#furion-browsing"),F=U.querySelector("#furion-heat-map"),I=U.querySelector("#furion-consistency"),b=U.querySelector("#furion-back"),L=U.querySelector("#furion-nav-toggle"),H=U.querySelector(".furion-toggle-btn-icon"),O=a.DOMACTIONS.createNode("div","furion-events-search");O.innerHTML+='\n    <div class="search-wrap">\n        <input id="furion-search-input" fr_atr="furion-node">\n        <div class="search-icons">\n            <img id="furion-clear-icon" fr_atr="furion-node" src='.concat(f[3],'>\n            <img id="furion-search-icon" fr_atr="furion-node" src=').concat(f[4],">\n        </div>\n    </div>\n    ");var j=a.DOMACTIONS.createNode("div","furion-popover"),Y=a.DOMACTIONS.createNode("img","furion-event-filter furion-popover-item","eventFilter","","",f[5]),Z=a.DOMACTIONS.createNode("div","furion-popover-content","","点击匹配当前页面埋点");j.appendChild(Y),j.appendChild(Z),O.appendChild(j),t.appendChild(O);var q=a.DOMACTIONS.createNode("div","furion-events-item");function $(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";g.innerHTML="",i.API.getEventList(A).then((function(A){if((z=A).length>0){g.innerHTML="";var n,r=B(z);try{for(r.s();!(n=r.n()).done;){var o=n.value;g.appendChild(G(o))}}catch(A){r.e(A)}finally{r.f()}}else g.appendChild(q);t.appendChild(g),e.appendChild(t)}))}function AA(A){A.stopPropagation()}q.innerHTML+="<span fr_atr='furion-node'>暂无数据</span>",e.addEventListener("click",(function(A){if(t&&!t.contains(A.target)){var e=(m=!m)?"block":"none";t.style.display=e,O.style.display=e,m&&$()}}),!1),Y.addEventListener("click",(function(A){var e;A.stopPropagation();var t=(null===(e=U.querySelector("#eventFilter"))||void 0===e?void 0:e.getAttribute("src"))===f[5];Y.setAttribute("src",t?f[6]:f[5]),Z.innerHTML=t?"点击匹配所有页面埋点":"点击匹配当前页面埋点",$(t?window.location.href:"")}),!1),document.addEventListener("mouseup",(function(A){A.target!==document.querySelector(".code-less-box")&&(m=!1,t.style.display="none")}),!1),U.querySelector(".furion-navbar-box").addEventListener("mouseup",(function(A){t&&(t.contains(A.target)||e.contains(A.target)||(m=!1,t.style.display="none"))}),!1),v.addEventListener("mousedown",(function(){window.addEventListener("blur",AA,!0),window.addEventListener("focusout",AA,!0),window.addEventListener("mouseup",AA,!0)})),v.addEventListener("click",(function(A){Q=p,s.Codeless.setPostMessage(),n.UTIL.removeTabActiveClass(),v.classList.add("navbar-tab-item-active"),N("flex"),K("none"),_("none"),k("none"),U.querySelector(".furion-navbar-box").style.width="1000px",W(),U.querySelector("#furion-loading").style.display="none",window.addEventListener("mouseover",P,!0),window.addEventListener("click",V,!0),D(),M(),d.Consistency.removeConsistencyDom(),A.stopPropagation()}),!1),y.addEventListener("mousedown",(function(){window.removeEventListener("blur",AA,!0),window.removeEventListener("focusout",AA,!0),setTimeout((function(){window.removeEventListener("mouseup",AA,!0)}),500)})),y.addEventListener("click",(function(){Q=C,n.UTIL.removeTabActiveClass(),y.classList.add("navbar-tab-item-active"),U.getElementById("pop-furion").style.display="none",N("flex"),K("none"),_("none"),k("none"),U.querySelector(".furion-navbar-box").style.width="1000px",a.DOMACTIONS.removeActive("active"),window.removeEventListener("scroll",J),a.DOMACTIONS.removeMarkedDom(),U.querySelector(".switch").checked=!1,U.querySelector(".switch-box").style.background="#ccc",W(),U.querySelector("#furion-loading").style.display="none",window.removeEventListener("mouseover",P,!0),window.removeEventListener("click",V,!0),D(),M(),d.Consistency.removeConsistencyDom()}),!1),F.addEventListener("mousedown",(function(){window.addEventListener("blur",AA,!0),window.addEventListener("focusout",AA,!0),window.addEventListener("mouseup",AA,!0)})),F.addEventListener("click",(function(A){F.classList.contains("navbar-tab-item-active")||u.Heatmap.runHeatmap(),Q=h,s.Codeless.setPostMessage(),n.UTIL.removeTabActiveClass(),F.classList.add("navbar-tab-item-active"),U.getElementById("pop-furion").style.display="none",N("none"),K("flex"),_("flex"),k("none"),U.querySelector(".furion-navbar-box").style.width="1000px",a.DOMACTIONS.removeActive("active"),window.removeEventListener("scroll",J),a.DOMACTIONS.removeMarkedDom(),U.querySelector(".switch").checked=!1,U.querySelector(".switch-box").style.background="#ccc",window.addEventListener("wheel",S,!0),window.addEventListener("resize",T),window.addEventListener("scroll",T,!0),window.removeEventListener("mouseover",P,!0),window.removeEventListener("click",V,!0),M(),d.Consistency.removeConsistencyDom(),A.stopPropagation()})),I.addEventListener("mousedown",(function(){window.addEventListener("blur",AA,!0),window.addEventListener("focusout",AA,!0),window.addEventListener("mouseup",AA,!0)})),I.addEventListener("click",(function(A){I.classList.contains("navbar-tab-item-active")||(Q=w,U.querySelector("#consistencyModeSelect").value=E.offline,x=E.offline,n.UTIL.removeTabActiveClass(),I.classList.add("navbar-tab-item-active"),U.getElementById("pop-furion").style.display="none",N("none"),K("flex"),_("flex"),k("flex"),U.querySelector(".furion-navbar-box").style.width="1300px",a.DOMACTIONS.removeActive("active"),window.removeEventListener("scroll",J),a.DOMACTIONS.removeMarkedDom(),U.querySelector(".switch").checked=!1,U.querySelector(".switch-box").style.background="#ccc",W(),D(),window.removeEventListener("mouseover",P,!0),window.removeEventListener("click",V,!0),A.stopPropagation())}));var eA=n.UTIL.getAppId();b.addEventListener("click",(function(){window.name="_self",window.open("".concat(n.UTIL.getConfig().frUrl,"?appId=").concat(eA))}),!1),L.addEventListener("click",(function(){A.classList.toggle("furion-navbar-close"),H.classList.toggle("furion-arrow-icon")}),!1),g.addEventListener("click",(function(A){var e=(A=A||window.event).target;z.some((function(A){return A.eventId===e.id}))&&r.default.confirm({title:"提示",content:"是否确认删除标记事件<span style=\"color:#5e7ce0\" fr_atr='furion-node'>".concat(e.getAttribute("name"),"</span>？")},{btn:["确认","取消"]},(function(){!function(A){var e,t=n.UTIL.getConfig().searchDomain;null===(e=n.UTIL.httpRequest("".concat(t,"/nopoint/deleteNoPointEvent?eventId=").concat(A.id),"get"))||void 0===e||e.then((function(e){var t;"success"===(null==e?void 0:e.status)?(o.TOAST.toast("删除成功",3e3),U.querySelector(".furion-eventsUl").removeChild(A.parentNode),U.getElementById("pop-furion").style.display="none",i.API.getEventList().then((function(A){z=A||[],U.querySelector(".switch").checked&&a.DOMACTIONS.showAllMarkedDom(z)}))):o.TOAST.toast(null==e||null===(t=e.result)||void 0===t?void 0:t.message,3e3)}))}(e)}))}),!1),g.addEventListener("mouseover",(function(A){var e=A.target,t=e.getAttribute("fr_fx"),n=e.getAttribute("fr_noPointEventType"),r=e.getAttribute("fr_eid"),o=e.getAttribute("fr_tc");if(!t||n!==l.CODELESS_CONFIG.noPointEventTypeMap.xpath&&"null"!==n)n===l.CODELESS_CONFIG.noPointEventTypeMap.id&&"null"!==r&&a.DOMACTIONS.getDomById(r,"hover",o);else{var i=X(e.getAttribute("fr_fuzzys"));a.DOMACTIONS.getPeersDom(t,i,"hover",o)}}),!1),g.addEventListener("mouseout",(function(A){A.target.getAttribute("fr_fx")&&a.DOMACTIONS.removeActive("hover")}),!1),g.addEventListener("click",(function(A){for(var e=A.target,t=U.querySelectorAll(".furion-events-item"),n=0;n<t.length;n++)t[n].classList.remove("furion-events-item-active");var r=e.getAttribute("fr_fx"),o=e.getAttribute("fr_noPointEventType"),i=e.getAttribute("fr_eventName"),s=e.getAttribute("fr_eventId"),c=e.getAttribute("fr_url"),u=e.getAttribute("fr_tc"),d=e.getAttribute("fr_ecls"),B=e.getAttribute("fr_matchPageUrl"),g=e.getAttribute("fr_matchPageType"),f=e.getAttribute("fr_eid");if(!r||o!==l.CODELESS_CONFIG.noPointEventTypeMap.xpath&&"null"!==o)if(o===l.CODELESS_CONFIG.noPointEventTypeMap.id&&"null"!==f){e.parentNode.classList.add("furion-events-item-active"),R(a.DOMACTIONS.getDomById(f,"active",u),"click","edit",r,i,s,c,u,d,g,B,o,f)}else o===l.CODELESS_CONFIG.noPointEventTypeMap.tc&&(e.parentNode.classList.add("furion-events-item-active"),a.DOMACTIONS.removeActive("active"),R(null,"click","edit",r,i,s,c,u,d,g,B,o,f));else{e.parentNode.classList.add("furion-events-item-active");var p=X(e.getAttribute("fr_fuzzys"));a.DOMACTIONS.getPeersDom(r,p,"active",u),R(a.DOMACTIONS.getDomByXpath(r),"click","edit",r,i,s,c,u,d,g,B,o,f)}}),!1),e.addEventListener("click",(function(A){var n,r=(A=A||window.event).target,o=U.querySelector("#furion-search-input"),i=null===(n=z)||void 0===n?void 0:n.filter((function(A){return A.eventName.includes(o.value)}));if("furion-search-input"===r.id&&(c.CONFIG.VISIT_INFO.browser.includes("IE")?o.onpropertychange=function(A){U.getElementById("furion-clear-icon").style.opacity=1}:o.oninput=function(A){U.getElementById("furion-clear-icon").style.opacity=1}),"furion-clear-icon"===r.id){U.querySelector("#furion-search-input").value="",U.getElementById("furion-clear-icon").style.opacity=0,g.innerHTML="";var s,a=B(z);try{for(a.s();!(s=a.n()).done;){var u=s.value;g.appendChild(G(u))}}catch(A){a.e(A)}finally{a.f()}t.appendChild(g),e.appendChild(t)}if("furion-search-icon"===r.id&&o.value){if(g.innerHTML="",i.length>0){var l,d=B(i);try{for(d.s();!(l=d.n()).done;){var f=l.value;g.appendChild(G(f))}}catch(A){d.e(A)}finally{d.f()}}else g.appendChild(q);t.appendChild(g),e.appendChild(t)}}),!1),e.addEventListener("keydown",(function(A){var n,r=(A=A||window.event).target,o=U.querySelector("#furion-search-input"),i=null===(n=z)||void 0===n?void 0:n.filter((function(A){return A.eventName.includes(o.value)}));if("furion-search-input"===r.id&&(13===A.keyCode||13===A.which)&&o.value){if(g.innerHTML="",i.length>0){var s,a=B(i);try{for(a.s();!(s=a.n()).done;){var c=s.value;g.appendChild(G(c))}}catch(A){a.e(A)}finally{a.f()}}else g.appendChild(q);t.appendChild(g),e.appendChild(t)}}),!1)},createEventsItem:G,getNewEventList:function(A){z=A},consistencyModeEnum:E}},"./src/newPop.js":function(A,e,t){"use strict";t.r(e),t.d(e,{NEWPOP:function(){return o}});t("./src/config.js");var n=t("./src/domActions.js"),r=null;var o={createPop:function(){r=document.querySelector(".code-less-box").shadowRoot;var A=n.DOMACTIONS.createNode("div","","pop-furion"),e=n.DOMACTIONS.createNode("div","furion-codeBox"),t=n.DOMACTIONS.createNode("div","furion-popClose","","x"),o=n.DOMACTIONS.createNode("div","btn-box"),i=n.DOMACTIONS.createNode("div","furion-submit","","提交"),s=n.DOMACTIONS.createNode("div","furion-query","","查询触发次数");o.appendChild(i),o.appendChild(s),A.appendChild(e),A.appendChild(t),A.appendChild(o),r.appendChild(A),n.DOMACTIONS.addCodeLessCSS("#pop-furion{border-radius:4px;width: 400px; border: 1px solid rgb(230, 233, 242); top:16%;right:20px;min-height:400px;background-color:rgb(241, 244, 248);box-shadow: 0px 3px 20px 0px rgb(64 98 255 / 20%);display:none;position:fixed;z-index:99999;cursor:move;max-height:1000px;overflow-y:auto;}\n         .furion-popClose{position:absolute;top:10px;right:10px;width:20px;height:20px;background:#d5d5db;border-radius:50%;color:#fff;text-align:center;line-height:17px;cursor:pointer;}.furion-popClose:hover{opacity:0.8;}\n         .furion-submit,.furion-query{width:100px;text-align:center;height:30px;line-height:30px;color:#fff;background:#5e7ce0;border-radius:4px;cursor:pointer;margin-bottom:20px;}.furion-submit:hover,.furion-query:hover{opacity:0.8;}\n         .furion-query {width:200px} .btn-box {width:100%;display:flex;justify-content:space-around}\n         @keyframes shake {10% {transform: rotate(5deg);} 20% {transform: rotate(-5deg);} 30% {transform: rotate(4deg);} 40% {transform: rotate(-4deg);} 50%{transform: rotate(3deg);} 60%{transform: rotate(-3deg);} 70%{transform: rotate(2deg);} 80%{transform: rotate(-2deg);} 90%{transform: rotate(1deg);} 100%{transform: rotate(0deg);}}\n         .pop-shake {animation: shake 0.5s 0.15s linear; -moz-animation: shake 1s 0.15s linear; -webkit-animation: shake 1s 0.15s linear ;-o-animation: shake 1s 0.15s linear ; \n        "),function(A){A.draggable="true";var e=0,t=0;function n(A){A.target.style.top=A.pageY-t-window.scrollY+"px",A.target.style.left=A.pageX-e-window.scrollX+"px"}A.addEventListener("dragstart",(function(A){e=A.clientX-A.target.offsetLeft,t=A.clientY-A.target.offsetTop})),A.addEventListener("drag",n),A.addEventListener("dragend",(function(e){A.removeEventListener("drag",n(e))}))}(A)}}},"./src/newToast.js":function(A,e,t){"use strict";t.r(e),t.d(e,{TOAST:function(){return o}});var n=t("./src/domActions.js"),r=null;var o={newToast:function(){r=document.querySelector(".code-less-box").shadowRoot;var A=n.DOMACTIONS.createNode("div","furion-toast_box"),e=n.DOMACTIONS.createNode("div","furion-toast-title"),t=n.DOMACTIONS.createNode("div","furion-toast");e.innerHTML="提示",A.appendChild(e),A.appendChild(t),r.appendChild(A),n.DOMACTIONS.addCodeLessCSS("@keyframes show {0% {right: -13%;opacity: 0;}100% {right: 1%;opacity: 1;}}@keyframes hide {0% {right: 1%; opacity: 1;}100% {right: -13%;opacity: 0;}}\n         .furion-toast_box{position: fixed;top:10%;right: 1%;z-index: 100000;border-radius: 5px;width: 200px;padding: 12px 16px;background: #5f6663;color: #fff;box-shadow: 0 0 5px #5f6663;display: none;}\n         .furion-toast{ box-sizing: border-box;width: max-content;font-size: 14px;margin-top:10px;}\n        ")},toast:function(A,e){var t=r.querySelector(".furion-toast"),n=r.querySelector(".furion-toast_box");t.innerHTML=A,n.style.animation="show 1s",n.style.display="inline-block",setTimeout((function(){n.style.animation="hide 1s",setTimeout((function(){n.style.display="none"}),800)}),e)}}},"./src/perf.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return u},getRedirectTime:function(){return w},getSuffix:function(){return C},getValidSize:function(){return d},isThirdParty:function(){return p}});var n=t("./src/util.js"),r=t("./src/fmp.js"),o=t("./src/log.js"),i=t("./src/vital.js"),s=t("./src/config.js");function a(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,o,i,s=[],a=!0,c=!1;try{if(o=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;a=!1}else for(;!(a=(n=o.call(t)).done)&&(s.push(n.value),s.length!==e);a=!0);}catch(A){c=!0,r=A}finally{try{if(!a&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(c)throw r}}return s}}(A,e)||function(A,e){if(A){if("string"==typeof A)return c(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?c(A,e):void 0}}(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function u(){var A;(function(){var A=window.performance,e=A.timing,t=A.getEntriesByType,r=e&&t&&s.CONFIG.VISIT_INFO.browser;r||n.UTIL.setFrVar({envNotOk:!0});return r})()&&(n.UTIL.getConfig().closeReportFMP||(A=function(){var A;"undefined"!=typeof Promise&&(A=new Promise((function(A){r.default.getFmp(3e3).then((function(e){s.CONFIG.GLOBAL_VARS.fmp=e,A()}))})));return A}()),function(A){(0,i.observeVitals)(),window.addEventListener("load",(function(){o.LOG.handlerPvReport("ld");var e=window.location;setTimeout((function(){void 0!==A?A.then((function(){(0,i.disconnectObservers)(),l(e)})):((0,i.disconnectObservers)(),l(e))}),1e3)}),!0)}(A))}function l(A){var e=n.UTIL.getReportUrl(A,!0),t=n.UTIL.getReportUrl(A,!1);if(n.UTIL.needReport(A)&&!n.UTIL.isFiltedUrl(t)){var r=function(A){var e=n.UTIL.getAppId(A),t=s.CONFIG.VISIT_INFO,r=t.pid,i=t.os,c=t.browser,u=o.LOG.getSsidAndPvid().ssid,l=performance.getEntriesByType("resource"),d=a(performance.getEntriesByType("navigation"),1)[0]||performance.timing,f=d.loadEventEnd,p=d.fetchStart,C=d.domInteractive,Q=n.UTIL.getPositiveInt(f-p),m=function(A,e){var t=w(),n=A.filter((function(A){var n=A||{},r=n.initiatorType,o=n.fetchStart,i=n.name;return!["xmlhttprequest","fetch","preflight"].includes(r)&&o-t<e&&!i.includes("data:image/png")})),r=performance.getEntries()[0]||{},o=r.initiatorType,i=r.fetchStart,s=r.encodedBodySize,a=r.decodedBodySize,c=r.transferSize,u=r.responseEnd;return n.unshift({name:"index.html",initiatorType:o,transferSize:c,decodedBodySize:a,encodedBodySize:s,duration:Math.round(u-i),fetchStart:i}),n}(l,Q),U=function(A,e){var t=w();return A.filter((function(A){return"xmlhttprequest"===(null==A?void 0:A.initiatorType)||"fetch"===(null==A?void 0:A.initiatorType)})).filter((function(A){return n.UTIL.apiCanReport(A.name)&&A.fetchStart-t<e})).map((function(A){return{url:encodeURIComponent(A.name),duration:Math.floor(A.duration)||0,decode_body_size:A.decodedBodySize||0,fetchStart:Math.floor(A.fetchStart-t),transfer_size:A.transferSize||0}}))}(l,Q+500),v=window.performance.memory||{},y=v.jsHeapSizeLimit,F=v.totalJSHeapSize,I=v.usedJSHeapSize,b={app_id:e,url:A,referer:document.referrer||"直接访问",api_list:U,resource_list:B(m),img_list:h(l,Q),resLoadTime:n.UTIL.getPositiveInt(f-C),pageTimes:g(),page_title:n.UTIL.cutTooLongStr(document.title),browser_type:c.split(": ")[0],browser_version:c.split(": ")[1],ssid:u,pid:r,screen:"".concat(window.screen.width,"*").concat(window.screen.height),os:i,effTy:navigator.connection?navigator.connection.effectiveType:"",dl:navigator.connection?Math.round(navigator.connection.downlink):-1,v:s.CONFIG.GLOBAL_VARS.VERSION,lhs:y||-1,ths:F||-1,uhs:I||-1};return b}(e);!function(A){A.extend=n.UTIL.getUserId(),A.uuid=s.CONFIG.GLOBAL_VARS.randomId,A.eno=n.UTIL.getCookie("login_uid"),A.userInfo=n.UTIL.getEncodedUserInfo()}(r),function(A){var e=document.getElementsByTagName("meta")["dragonfly.sequenceID"];A.sqId=e?e.content:null}(r),function(A){A.bucket=n.UTIL.getABtestParams()}(r);var i=s.CONFIG.GLOBAL_VARS.domain;n.UTIL.httpRequest("".concat(i,"/api/pages"),"post",r).catch((function(A){}))}}function d(A){var e=null!=A?A:-1;return(A<0||A>3e8||isNaN(A))&&(e=-1),e}function B(A){var e=w();return A.map((function(A){var t=A||{},n=t.fetchStart,r=t.duration,o=t.decodedBodySize,i=t.encodedBodySize,s=t.transferSize,a=t.initiatorType;return{fetchStart:Math.round(n-e),duration:Math.round(r),name:A.name.split(/[?|#|,]/)[0],decodedBodySize:d(o),encodedBodySize:d(i),transferSize:d(s),type:a}}))}function g(){var A,e,t,n,r={},o=performance.getEntriesByType("navigation");if(o&&o[0]){var i=function(A){var e=A.requestStart,t=A.redirectStart,n=A.redirectEnd,r=A.domainLookupStart,o=A.domainLookupEnd,i=A.secureConnectionStart,s=A.connectEnd,a=A.responseStart,c=A.responseEnd,u=A.loadEventEnd,l=A.fetchStart,d=A.domInteractive,B=A.domLoading,g=A.domComplete,f={requestStart:e,redirectStart:t,redirectEnd:n,domainLookupStart:r,domainLookupEnd:o,secureConnectionStart:i,connectEnd:s,responseStart:a,responseEnd:c,loadEventEnd:u,fetchStart:l,domInteractive:d,domLoading:B,domComplete:g};return f}(o[0]);A=i,e=performance.timing,t=e.domLoading,n=e.navigationStart,A.domLoading=t-n,r=f(i)}else r=f(performance.timing);return r}function f(A){var e=A.requestStart,t=A.redirectStart,r=A.redirectEnd,o=A.domainLookupStart,c=A.domainLookupEnd,u=A.secureConnectionStart,l=A.connectEnd,d=A.responseStart,B=A.responseEnd,g=A.loadEventEnd,f=A.fetchStart,p=A.domInteractive,h=A.domLoading,w=A.domComplete,C=(0,i.getVitalsVal)(),Q=C.lcp,m=C.fid,U=C.cls,v={loadTime:n.UTIL.getPositiveInt(g-f),parseHTML:n.UTIL.getPositiveInt(p-h),execTime:n.UTIL.getPositiveInt(w-p),fp:n.UTIL.getPositiveInt(h-f),whiteTime:n.UTIL.getPositiveInt(h-f),domtti:n.UTIL.getPositiveInt(p-f),navigation:n.UTIL.getPositiveInt(r-t),appCache:n.UTIL.getPositiveInt(o-f),dns:n.UTIL.getPositiveInt(c-o),tcp:n.UTIL.getPositiveInt(l-c),ssl:u?n.UTIL.getPositiveInt(l-u):0,response:n.UTIL.getPositiveInt(d-l),contentTrans:n.UTIL.getPositiveInt(B-d),network:n.UTIL.getPositiveInt(B-f),domParse:n.UTIL.getPositiveInt(p-B),resLoad:n.UTIL.getPositiveInt(g-p),fmp:n.UTIL.getPositiveInt(s.CONFIG.GLOBAL_VARS.fmp)||null,ttfb:n.UTIL.getPositiveInt(d-e),lcp:Q,fid:m,cls:U};return function(A){var e=a(performance.getEntriesByType("paint"),2),t=e[0],r=e[1],o=(t||{}).startTime,i=(r||{}).startTime;o&&(A.fpNew=n.UTIL.getPositiveInt(o));i&&(A.fcp=n.UTIL.getPositiveInt(i))}(v),v}function p(A){return void 0!==s.CONFIG.THIRD_PARTY_DOMAIN.find((function(e){return A.split(/[?|#]/)[0].includes(e)}))}function h(A,e){return A.filter((function(A){var t=A||{},n=t.name,r=t.fetchStart,o=t.initiatorType,i=s.CONFIG.IMG_SUFFIX.includes(C(n))||"img"===o,a=r-w()<e;return i&&a&&!p(n)})).map((function(A){return{name:A.name,decodedBodySize:d(A.decodedBodySize)}}))}function w(){var A=window.performance.timing;return A.fetchStart-A.navigationStart}function C(A){return A.split(/[?|#]/)[0].split(".").pop()}},"./src/resources.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return o}});var n=t("./src/log.js"),r=t("./src/util.js");const o=function(){var A=[],e=["script","link","img","css"],t=1,o=0;function i(t,i){var s=t||{},a=s.name,c=s.fetchStart,u=s.initiatorType,l=s.duration,d=s.startTime,B=s.connectStart,g=s.requestStart,f=s.connectEnd,p=Math.round(r.UTIL.getPositiveInt(g-d)+r.UTIL.getPositiveInt(f-B)),h=a.split(/[?|#|,]/)[0];e.includes(u)&&h&&!A.includes(h)&&(n.LOG.report("resource",h,{dr:Math.round(l),fs:Math.round(c),ts:Math.round(performance.now()),enm:u,sc:i,stt:p}),i===o&&A.length>250&&(A.shift(),A.push(h)))}function s(A){A.forEach((function(A){i(A,t)}))}window.addEventListener("error",(function(A){var e=A.target,t=void 0===e?{}:e,n=t.src,r=void 0===n?"":n,s=t.href,a=r||(void 0===s?"":s),c=window.performance.getEntries("resource").find((function(A){return A.name===a}));c&&i(c,o)}),!0),r.UTIL.addLoadEvent((function(){s(window.performance.getEntries("resource"));try{var A=new PerformanceObserver((function(A){s(A.getEntries())}));A.observe({entryTypes:["resource"]})}catch(A){}}))}},"./src/rtti.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return w}});var n=t("./src/config.js"),r=t("./src/util.js"),o=t("./src/rttiUtil.js"),i=t("./src/log.js");function s(A){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},s(A)}function a(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function c(A,e,t){return(e=function(A){var e=function(A,e){if("object"!=s(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var n=t.call(A,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==s(e)?e:e+""}(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}var u={RTTI:"rtti",PCP:"pcp"},l=!1,d=!1,B=0,g=null,f=!1,p=function(A,e){return!(A===u.RTTI&&l||A===u.PCP&&d)&&(!(g&&r.UTIL.getReportUrl(window.location,!1)!==r.UTIL.getReportUrl(g,!1)||f)&&(A===u.PCP||!(++B<e)))},h=function(A,e){var t,s=0,a=0,c=performance.getEntriesByType("navigation");if(c&&c[0])s=parseInt(performance.now()-c[0].fetchStart),a=parseInt(c[0].responseStart-c[0].requestStart);else{var l=window.performance.timing;s=Date.now()-l.fetchStart,a=l.responseStart-l.requestStart}if(A===u.RTTI&&(window.FURION.rtti_for_yuntan=s),A===u.PCP&&(window.FURION.pcp_for_yuntan=s),r.UTIL.needReport()){var d=r.UTIL.getReportUrl(g,!0),B=r.UTIL.getAppId(d),f=n.CONFIG.VISIT_INFO,p=f.pid,h=f.os,w=f.browser,C=i.LOG.getSsidAndPvid().ssid,Q={duration:s,type:A,appId:e||B,uid:r.UTIL.getUserId(),uuid:n.CONFIG.GLOBAL_VARS.randomId,eno:r.UTIL.getCookie("login_uid"),userInfo:r.UTIL.getEncodedUserInfo(),url:d,ua:navigator.userAgent,os:h,brt:w.split(": ")[0],brv:w.split(": ")[1],pid:p,ssid:C,region:o.rttiUTIL.getRegion(),station:o.rttiUTIL.getStation(),resources:o.rttiUTIL.getResources(),fp:parseInt((null===(t=window.performance.getEntriesByType("paint")[0])||void 0===t?void 0:t.startTime)||0),ttfb:a,sr:"".concat(window.screen.width,"*").concat(window.screen.height),v:n.CONFIG.GLOBAL_VARS.VERSION,bucket:r.UTIL.getABtestParams()},m=n.CONFIG.GLOBAL_VARS.domain;r.UTIL.httpRequest("".concat(m,"/api/rtti/reportTime"),"post",Q).catch((function(A){}))}};const w=function(){window.tinyMonitor=window.TinyMonitor={},r.UTIL.addLoadEvent((function(){g=function(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?a(Object(t),!0).forEach((function(e){c(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):a(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}({},window.location)}));var A=function(A){g&&r.UTIL.getReportUrl(A,!1)!==r.UTIL.getReportUrl(g,!1)&&(f=!0)};window.addEventListener("popstate",(function(e){A(e.target.location)})),window.addEventListener("replaceState",(function(e){A(e.target.location)})),window.addEventListener("pushState",(function(e){A(e.target.location)})),r.UTIL.getConfig().hashMode&&window.addEventListener("hashchange",(function(e){A(e.target.location)})),window.FURION.reportCustomTime=window.TinyMonitor.reportCustomTime=function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;Object.values(u).includes(A)&&p(A,t)&&(A===u.RTTI&&(window.setTimeout((function(){h(A,e)}),0),l=!0),A===u.PCP&&(h(A,e),d=!0))},window.FURION.setUid=window.TinyMonitor.setUid=function(A){r.UTIL.setUid(A)},window.FURION.setAppId=window.TinyMonitor.setAppId=function(A){window.__fr&&window.__fr.config&&(window.__fr.config.appId=A)}}},"./src/rttiUtil.js":function(A,e,t){"use strict";t.r(e),t.d(e,{rttiUTIL:function(){return r}});var n=function(A){var e=A;if(e&&e.indexOf("_")>-1){var t=e.match(/^(DeC)_([^_]+)_(.+)$/);e=t?t[2]:e.slice(0,e.indexOf("_"))}return e},r={getRegion:function(){for(var A=window.location.search.substring(1).split("&"),e=0;e<A.length;e++){var t=A[e].split("=");if("region"===t[0])return n(t[1])}return""},getStation:function(){var A=0,e=window.location.hostname;return e.indexOf("-intl")>-1?A=2:/^(console.huaweicloud.com|account.huaweicloud.com|auth.huaweicloud.com|storage.huaweicloud.com|console.ulanqab.huawei.com|console-odin.ulanqab.huawei.com|account.ulanqab.huawei.com)$/.test(e)?A=1:/^(la-north-2|la-south-2|sa-brazil-1)-console.huaweicloud.com$/.test(e)&&(A=3),A},getResources:function(){for(var A=[],e=performance.getEntriesByType("resource"),t=e.length,n=0;n<t;n++){var r=e[n],o=r.name.replace(/^https?\:\/\//i,"");-1!==o.indexOf("?")&&(o=o.split("?")[0]),A.push({d:Math.round(r.duration),s:r.encodedBodySize,u:o.split(/[?|#|,]/)[0],t:r.initiatorType,st:r.startTime})}var i=performance.getEntries()[0];return A.unshift({d:Math.round(i.responseEnd-i.fetchStart),s:i.encodedBodySize,u:"index.html",t:"doc",st:i.startTime}),A}}},"./src/scripts/heatmap.min.js":function(A,e,t){var n,r,o;o=function(){var A={defaultRadius:40,defaultRenderer:"canvas2d",defaultGradient:{.25:"rgb(0,0,255)",.55:"rgb(0,255,0)",.85:"yellow",1:"rgb(255,0,0)"},defaultMaxOpacity:1,defaultMinOpacity:0,defaultBlur:.85,defaultXField:"x",defaultYField:"y",defaultValueField:"value",plugins:{}},e=function(){var e=function(A){this._coordinator={},this._data=[],this._radi=[],this._min=0,this._max=1,this._xField=A.xField||A.defaultXField,this._yField=A.yField||A.defaultYField,this._valueField=A.valueField||A.defaultValueField,A.radius&&(this._cfgRadius=A.radius)},t=A.defaultRadius;return e.prototype={_organiseData:function(A,e){var n=A[this._xField],r=A[this._yField],o=this._radi,i=this._data,s=this._max,a=this._min,c=A[this._valueField]||1,u=A.radius||this._cfgRadius||t;return i[n]||(i[n]=[],o[n]=[]),i[n][r]?i[n][r]+=c:(i[n][r]=c,o[n][r]=u),i[n][r]>s?(e?this.setDataMax(i[n][r]):this._max=i[n][r],!1):{x:n,y:r,value:c,radius:u,min:a,max:s}},_unOrganizeData:function(){var A=[],e=this._data,t=this._radi;for(var n in e)for(var r in e[n])A.push({x:n,y:r,radius:t[n][r],value:e[n][r]});return{min:this._min,max:this._max,data:A}},_onExtremaChange:function(){this._coordinator.emit("extremachange",{min:this._min,max:this._max})},addData:function(){if(arguments[0].length>0)for(var A=arguments[0],e=A.length;e--;)this.addData.call(this,A[e]);else{var t=this._organiseData(arguments[0],!0);t&&this._coordinator.emit("renderpartial",{min:this._min,max:this._max,data:[t]})}return this},setData:function(A){var e=A.data,t=e.length;this._data=[],this._radi=[];for(var n=0;n<t;n++)this._organiseData(e[n],!1);return this._max=A.max,this._min=A.min||0,this._onExtremaChange(),this._coordinator.emit("renderall",this._getInternalData()),this},removeData:function(){},setDataMax:function(A){return this._max=A,this._onExtremaChange(),this._coordinator.emit("renderall",this._getInternalData()),this},setDataMin:function(A){return this._min=A,this._onExtremaChange(),this._coordinator.emit("renderall",this._getInternalData()),this},setCoordinator:function(A){this._coordinator=A},_getInternalData:function(){return{max:this._max,min:this._min,data:this._data,radi:this._radi}},getData:function(){return this._unOrganizeData()}},e}(),t=function(){var A=function(A){var e=A.gradient||A.defaultGradient,t=document.createElement("canvas"),n=t.getContext("2d");t.width=256,t.height=1;var r=n.createLinearGradient(0,0,256,1);for(var o in e)r.addColorStop(o,e[o]);return n.fillStyle=r,n.fillRect(0,0,256,1),n.getImageData(0,0,256,1).data},e=function(A,e){var t=document.createElement("canvas"),n=t.getContext("2d"),r=A,o=A;if(t.width=t.height=2*A,1==e)n.beginPath(),n.arc(r,o,A,0,2*Math.PI,!1),n.fillStyle="rgba(0,0,0,1)",n.fill();else{var i=n.createRadialGradient(r,o,A*e,r,o,A);i.addColorStop(0,"rgba(0,0,0,1)"),i.addColorStop(1,"rgba(0,0,0,0)"),n.fillStyle=i,n.fillRect(0,0,2*A,2*A)}return t};function t(e){var t=e.container,n=this.shadowCanvas=document.createElement("canvas"),r=this.canvas=e.canvas||document.createElement("canvas"),o=(this._renderBoundaries=[1e4,1e4,0,0],getComputedStyle(e.container)||{});r.className="heatmap-canvas",this._width=r.width=n.width=e.width||+o.width.replace(/px/,""),this._height=r.height=n.height=e.height||+o.height.replace(/px/,""),this.shadowCtx=n.getContext("2d"),this.ctx=r.getContext("2d"),r.style.cssText=n.style.cssText="position:absolute;left:0;top:0;",t.style.position="relative",t.appendChild(r),this._palette=A(e),this._templates={},this._setStyles(e)}return t.prototype={renderPartial:function(A){A.data.length>0&&(this._drawAlpha(A),this._colorize())},renderAll:function(A){this._clear(),A.data.length>0&&(this._drawAlpha(function(A){for(var e=[],t=A.min,n=A.max,r=A.radi,o=(A=A.data,Object.keys(A)),i=o.length;i--;)for(var s=o[i],a=Object.keys(A[s]),c=a.length;c--;){var u=a[c],l=A[s][u],d=r[s][u];e.push({x:s,y:u,value:l,radius:d})}return{min:t,max:n,data:e}}(A)),this._colorize())},_updateGradient:function(e){this._palette=A(e)},updateConfig:function(A){A.gradient&&this._updateGradient(A),this._setStyles(A)},setDimensions:function(A,e){this._width=A,this._height=e,this.canvas.width=this.shadowCanvas.width=A,this.canvas.height=this.shadowCanvas.height=e},_clear:function(){this.shadowCtx.clearRect(0,0,this._width,this._height),this.ctx.clearRect(0,0,this._width,this._height)},_setStyles:function(A){this._blur=0==A.blur?0:A.blur||A.defaultBlur,A.backgroundColor&&(this.canvas.style.backgroundColor=A.backgroundColor),this._width=this.canvas.width=this.shadowCanvas.width=A.width||this._width,this._height=this.canvas.height=this.shadowCanvas.height=A.height||this._height,this._opacity=255*(A.opacity||0),this._maxOpacity=255*(A.maxOpacity||A.defaultMaxOpacity),this._minOpacity=255*(A.minOpacity||A.defaultMinOpacity),this._useGradientOpacity=!!A.useGradientOpacity},_drawAlpha:function(A){for(var t=this._min=A.min,n=this._max=A.max,r=(A=A.data||[]).length,o=1-this._blur;r--;){var i,s=A[r],a=s.x,c=s.y,u=s.radius,l=Math.min(s.value,n),d=a-u,B=c-u,g=this.shadowCtx;this._templates[u]?i=this._templates[u]:this._templates[u]=i=e(u,o);var f=(l-t)/(n-t);g.globalAlpha=f<.01?.01:f,g.drawImage(i,d,B),d<this._renderBoundaries[0]&&(this._renderBoundaries[0]=d),B<this._renderBoundaries[1]&&(this._renderBoundaries[1]=B),d+2*u>this._renderBoundaries[2]&&(this._renderBoundaries[2]=d+2*u),B+2*u>this._renderBoundaries[3]&&(this._renderBoundaries[3]=B+2*u)}},_colorize:function(){var A=this._renderBoundaries[0],e=this._renderBoundaries[1],t=this._renderBoundaries[2]-A,n=this._renderBoundaries[3]-e,r=this._width,o=this._height,i=this._opacity,s=this._maxOpacity,a=this._minOpacity,c=this._useGradientOpacity;A<0&&(A=0),e<0&&(e=0),A+t>r&&(t=r-A),e+n>o&&(n=o-e);for(var u=this.shadowCtx.getImageData(A,e,t,n),l=u.data,d=l.length,B=this._palette,g=3;g<d;g+=4){var f,p=l[g],h=4*p;h&&(f=i>0?i:p<s?p<a?a:p:s,l[g-3]=B[h],l[g-2]=B[h+1],l[g-1]=B[h+2],l[g]=c?B[h+3]:f)}u.data=l,this.ctx.putImageData(u,A,e),this._renderBoundaries=[1e3,1e3,0,0]},getValueAt:function(A){var e=this.shadowCtx.getImageData(A.x,A.y,1,1).data[3],t=this._max,n=this._min;return Math.abs(t-n)*(e/255)|0},getDataURL:function(){return this.canvas.toDataURL()}},t}(),n=function(){var e=!1;return"canvas2d"===A.defaultRenderer&&(e=t),e}(),r=function(){for(var A={},e=arguments.length,t=0;t<e;t++){var n=arguments[t];for(var r in n)A[r]=n[r]}return A},o=function(){var t=function(){function A(){this.cStore={}}return A.prototype={on:function(A,e,t){var n=this.cStore;n[A]||(n[A]=[]),n[A].push((function(A){return e.call(t,A)}))},emit:function(A,e){var t=this.cStore;if(t[A])for(var n=t[A].length,r=0;r<n;r++)(0,t[A][r])(e)}},A}();function o(){var o=this._config=r(A,arguments[0]||{});if(this._coordinator=new t,o.plugin){var i=o.plugin;if(!A.plugins[i])throw new Error("Plugin '"+i+"' not found. Maybe it was not registered.");var s=A.plugins[i];this._renderer=new s.renderer(o),this._store=new s.store(o)}else this._renderer=new n(o),this._store=new e(o);!function(A){var e=A._renderer,t=A._coordinator,n=A._store;t.on("renderpartial",e.renderPartial,e),t.on("renderall",e.renderAll,e),t.on("extremachange",(function(e){A._config.onExtremaChange&&A._config.onExtremaChange({min:e.min,max:e.max,gradient:A._config.gradient||A._config.defaultGradient})})),n.setCoordinator(t)}(this)}return o.prototype={addData:function(){return this._store.addData.apply(this._store,arguments),this},removeData:function(){return this._store.removeData&&this._store.removeData.apply(this._store,arguments),this},setData:function(){return this._store.setData.apply(this._store,arguments),this},setDataMax:function(){return this._store.setDataMax.apply(this._store,arguments),this},setDataMin:function(){return this._store.setDataMin.apply(this._store,arguments),this},configure:function(A){return this._config=r(this._config,A),this._renderer.updateConfig(this._config),this._coordinator.emit("renderall",this._store._getInternalData()),this},repaint:function(){return this._coordinator.emit("renderall",this._store._getInternalData()),this},getData:function(){return this._store.getData()},getDataURL:function(){return this._renderer.getDataURL()},getValueAt:function(A){return this._store.getValueAt?this._store.getValueAt(A):this._renderer.getValueAt?this._renderer.getValueAt(A):null}},o}(),i={create:function(A){return new o(A)},register:function(e,t){A.plugins[e]=t}};return i},A.exports?A.exports=o():void 0===(r="function"==typeof(n=o)?n.call(e,t,e,A):n)||(A.exports=r)},"./src/scripts/html2canvas.min.js":function(A,e,t){var n,r,o;function i(A){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},i(A)}o=function(){"use strict";var A=function(e,t){return(A=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(e,t)};function e(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}A(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var t=function(){return(t=Object.assign||function(A){for(var e,t=1,n=arguments.length;t<n;t++)for(var r in e=arguments[t])Object.prototype.hasOwnProperty.call(e,r)&&(A[r]=e[r]);return A}).apply(this,arguments)};function n(A,e,t,n){return new(t=t||Promise)((function(r,o){function i(A){try{a(n.next(A))}catch(A){o(A)}}function s(A){try{a(n.throw(A))}catch(A){o(A)}}function a(A){var e;A.done?r(A.value):((e=A.value)instanceof t?e:new t((function(A){A(e)}))).then(i,s)}a((n=n.apply(A,e||[])).next())}))}function r(A,e){var t,n,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},i={next:s(0),throw:s(1),return:s(2)};return"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,n&&(r=2&i[0]?n.return:i[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,i[1])).done)return r;switch(n=0,(i=r?[2&i[0],r.value]:i)[0]){case 0:case 1:r=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,n=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!(r=0<(r=o.trys).length&&r[r.length-1])&&(6===i[0]||2===i[0])){o=0;continue}if(3===i[0]&&(!r||i[1]>r[0]&&i[1]<r[3])){o.label=i[1];break}if(6===i[0]&&o.label<r[1]){o.label=r[1],r=i;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(i);break}r[2]&&o.ops.pop(),o.trys.pop();continue}i=e.call(A,o)}catch(A){i=[6,A],n=0}finally{t=r=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function o(A,e,t){if(t||2===arguments.length)for(var n,r=0,o=e.length;r<o;r++)!n&&r in e||((n=n||Array.prototype.slice.call(e,0,r))[r]=e[r]);return A.concat(n||e)}var s=(a.prototype.add=function(A,e,t,n){return new a(this.left+A,this.top+e,this.width+t,this.height+n)},a.fromClientRect=function(A,e){return new a(e.left+A.windowBounds.left,e.top+A.windowBounds.top,e.width,e.height)},a.fromDOMRectList=function(A,e){return e=Array.from(e).find((function(A){return 0!==A.width})),e?new a(e.left+A.windowBounds.left,e.top+A.windowBounds.top,e.width,e.height):a.EMPTY},a.EMPTY=new a(0,0,0,0),a);function a(A,e,t,n){this.left=A,this.top=e,this.width=t,this.height=n}for(var c=function(A,e){return s.fromClientRect(A,e.getBoundingClientRect())},u=function(A){for(var e=[],t=0,n=A.length;t<n;){var r,o=A.charCodeAt(t++);55296<=o&&o<=56319&&t<n?56320==(64512&(r=A.charCodeAt(t++)))?e.push(((1023&o)<<10)+(1023&r)+65536):(e.push(o),t--):e.push(o)}return e},l=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var n=[],r=-1,o="";++r<t;){var i=A[r];i<=65535?n.push(i):(i-=65536,n.push(55296+(i>>10),i%1024+56320)),(r+1===t||16384<n.length)&&(o+=String.fromCharCode.apply(String,n),n.length=0)}return o},d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",B="undefined"==typeof Uint8Array?[]:new Uint8Array(256),g=0;g<64;g++)B[d.charCodeAt(g)]=g;for(var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p="undefined"==typeof Uint8Array?[]:new Uint8Array(256),h=0;h<64;h++)p[f.charCodeAt(h)]=h;function w(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))}var C=(Q.prototype.get=function(A){var e;if(0<=A){if(A<55296||56319<A&&A<=65535)return e=this.index[A>>5],this.data[e=(e<<2)+(31&A)];if(A<=65535)return e=this.index[2048+(A-55296>>5)],this.data[e=(e<<2)+(31&A)];if(A<this.highStart)return e=this.index[e=2080+(A>>11)],e=this.index[e+=A>>5&63],this.data[e=(e<<2)+(31&A)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},Q);function Q(A,e,t,n,r,o){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=n,this.index=r,this.data=o}for(var m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",U="undefined"==typeof Uint8Array?[]:new Uint8Array(256),v=0;v<64;v++)U[m.charCodeAt(v)]=v;function y(A,e,t,n){var r=n[t];if(Array.isArray(A)?-1!==A.indexOf(r):A===r)for(var o=t;o<=n.length;){if((s=n[++o])===e)return 1;if(s!==H)break}if(r===H)for(o=t;0<o;){var i=n[--o];if(Array.isArray(A)?-1!==A.indexOf(i):A===i)for(var s,a=t;a<=n.length;){if((s=n[++a])===e)return 1;if(s!==H)break}if(i!==H)break}}function F(A,e){for(var t=A;0<=t;){var n=e[t];if(n!==H)return n;t--}return 0}var I,b,E,L,x,H=10,S=13,T=15,O=17,M=18,D=19,j=20,N=21,K=22,_=24,k=25,G=26,P=27,R=28,V=30,z=32,X=33,J=34,W=35,Y=37,Z=38,q=39,$=40,AA=42,eA=[9001,65288],tA="×",nA=(E=function(A){var e,t,n,r,o=.75*A.length,i=A.length,s=0;"="===A[A.length-1]&&(o--,"="===A[A.length-2]&&o--),o=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(o);for(var a=Array.isArray(o)?o:new Uint8Array(o),c=0;c<i;c+=4)e=p[A.charCodeAt(c)],t=p[A.charCodeAt(c+1)],n=p[A.charCodeAt(c+2)],r=p[A.charCodeAt(c+3)],a[s++]=e<<2|t>>4,a[s++]=(15&t)<<4|n>>2,a[s++]=(3&n)<<6|63&r;return o}("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"),L=Array.isArray(E)?function(A){for(var e=A.length,t=[],n=0;n<e;n+=4)t.push(A[n+3]<<24|A[n+2]<<16|A[n+1]<<8|A[n]);return t}(E):new Uint32Array(E),x=Array.isArray(E)?function(A){for(var e=A.length,t=[],n=0;n<e;n+=2)t.push(A[n+1]<<8|A[n]);return t}(E):new Uint16Array(E),I=w(x,12,L[4]/2),b=2===L[5]?w(x,(24+L[4])/2):(E=L,x=Math.ceil((24+L[4])/4),E.slice?E.slice(x,b):new Uint32Array(Array.prototype.slice.call(E,x,b))),new C(L[0],L[1],L[2],L[3],I,b)),rA=[V,36],oA=[1,2,3,5],iA=[H,8],sA=[P,G],aA=oA.concat(iA),cA=[Z,q,$,J,W],uA=[T,S],lA=(dA.prototype.slice=function(){return l.apply(void 0,this.codePoints.slice(this.start,this.end))},dA);function dA(A,e,t,n){this.codePoints=A,this.required="!"===e,this.start=t,this.end=n}function BA(A,e){var t=u(A),n=(e=function(A,e){var t=(r=function(A,e){void 0===e&&(e="strict");var t=[],n=[],r=[];return A.forEach((function(A,o){var i=nA.get(A);return 50<i?(r.push(!0),i-=50):r.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A)?(n.push(o),t.push(16)):4!==i&&11!==i?(n.push(o),31===i?t.push("strict"===e?N:Y):i===AA||29===i?t.push(V):43===i?131072<=A&&A<=196605||196608<=A&&A<=262141?t.push(Y):t.push(V):void t.push(i)):0===o?(n.push(o),t.push(V)):(i=t[o-1],-1===aA.indexOf(i)?(n.push(n[o-1]),t.push(i)):(n.push(o),t.push(V)))})),[n,t,r]}(A,(e=e||{lineBreak:"normal",wordBreak:"normal"}).lineBreak))[0],n=r[1],r=r[2];return[t,n="break-all"===e.wordBreak||"break-word"===e.wordBreak?n.map((function(A){return-1!==[k,V,AA].indexOf(A)?Y:A})):n,"keep-all"===e.wordBreak?r.map((function(e,t){return e&&19968<=A[t]&&A[t]<=40959})):void 0]}(t,e))[0],r=e[1],o=e[2],i=t.length,s=0,a=0;return{next:function(){if(i<=a)return{done:!0,value:null};for(var A=tA;a<i&&(A=function(A,e,t,n,r){if(0===t[n])return tA;var o=n-1;if(Array.isArray(r)&&!0===r[o])return tA;var i,s=o-1,a=1+o,c=e[o];if(n=0<=s?e[s]:0,r=e[a],2===c&&3===r)return tA;if(-1!==oA.indexOf(c))return"!";if(-1!==oA.indexOf(r))return tA;if(-1!==iA.indexOf(r))return tA;if(8===F(o,e))return"÷";if(11===nA.get(A[o]))return tA;if((c===z||c===X)&&11===nA.get(A[a]))return tA;if(7===c||7===r)return tA;if(9===c)return tA;if(-1===[H,S,T].indexOf(c)&&9===r)return tA;if(-1!==[O,M,D,_,R].indexOf(r))return tA;if(F(o,e)===K)return tA;if(y(23,K,o,e))return tA;if(y([O,M],N,o,e))return tA;if(y(12,12,o,e))return tA;if(c===H)return"÷";if(23===c||23===r)return tA;if(16===r||16===c)return"÷";if(-1!==[S,T,N].indexOf(r)||14===c)return tA;if(36===n&&-1!==uA.indexOf(c))return tA;if(c===R&&36===r)return tA;if(r===j)return tA;if(-1!==rA.indexOf(r)&&c===k||-1!==rA.indexOf(c)&&r===k)return tA;if(c===P&&-1!==[Y,z,X].indexOf(r)||-1!==[Y,z,X].indexOf(c)&&r===G)return tA;if(-1!==rA.indexOf(c)&&-1!==sA.indexOf(r)||-1!==sA.indexOf(c)&&-1!==rA.indexOf(r))return tA;if(-1!==[P,G].indexOf(c)&&(r===k||-1!==[K,T].indexOf(r)&&e[1+a]===k)||-1!==[K,T].indexOf(c)&&r===k||c===k&&-1!==[k,R,_].indexOf(r))return tA;if(-1!==[k,R,_,O,M].indexOf(r))for(var u=o;0<=u;){if((i=e[u])===k)return tA;if(-1===[R,_].indexOf(i))break;u--}if(-1!==[P,G].indexOf(r))for(u=-1!==[O,M].indexOf(c)?s:o;0<=u;){if((i=e[u])===k)return tA;if(-1===[R,_].indexOf(i))break;u--}if(Z===c&&-1!==[Z,q,J,W].indexOf(r)||-1!==[q,J].indexOf(c)&&-1!==[q,$].indexOf(r)||-1!==[$,W].indexOf(c)&&r===$)return tA;if(-1!==cA.indexOf(c)&&-1!==[j,G].indexOf(r)||-1!==cA.indexOf(r)&&c===P)return tA;if(-1!==rA.indexOf(c)&&-1!==rA.indexOf(r))return tA;if(c===_&&-1!==rA.indexOf(r))return tA;if(-1!==rA.concat(k).indexOf(c)&&r===K&&-1===eA.indexOf(A[a])||-1!==rA.concat(k).indexOf(r)&&c===M)return tA;if(41===c&&41===r){for(var l=t[o],d=1;0<l&&41===e[--l];)d++;if(d%2!=0)return tA}return c===z&&r===X?tA:"÷"}(t,r,n,++a,o))===tA;);if(A===tA&&a!==i)return{done:!0,value:null};var e=new lA(t,A,s,a);return s=a,{value:e,done:!1}}}}function gA(A){return 48<=A&&A<=57}function fA(A){return gA(A)||65<=A&&A<=70||97<=A&&A<=102}function pA(A){return 10===A||9===A||32===A}function hA(A){return 97<=(t=e=A)&&t<=122||65<=e&&e<=90||128<=A||95===A;var e,t}function wA(A){return hA(A)||gA(A)||45===A}function CA(A,e){return 92===A&&10!==e}function QA(A,e,t){return 45===A?hA(e)||CA(e,t):!!hA(A)||92===A&&10!==e}function mA(A,e,t){return 43===A||45===A?!!gA(e)||46===e&&gA(t):gA(46===A?e:A)}var UA={type:2},vA={type:3},yA={type:4},FA={type:13},IA={type:8},bA={type:21},EA={type:9},LA={type:10},xA={type:11},HA={type:12},SA={type:14},TA={type:23},OA={type:1},MA={type:25},DA={type:24},jA={type:26},NA={type:27},KA={type:28},_A={type:29},kA={type:31},GA={type:32},PA=(RA.prototype.write=function(A){this._value=this._value.concat(u(A))},RA.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==GA;)A.push(e),e=this.consumeToken();return A},RA.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),n=this.peekCodePoint(2);if(wA(e)||CA(t,n)){var r=QA(e,t,n)?2:1;return{type:5,value:this.consumeName(),flags:r}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),FA;break;case 39:return this.consumeStringToken(39);case 40:return UA;case 41:return vA;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),SA;break;case 43:if(mA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return yA;case 45:if(mA(n=A,r=this.peekCodePoint(0),i=this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(QA(n,r,i))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(45===r&&62===i)return this.consumeCodePoint(),this.consumeCodePoint(),DA;break;case 46:if(mA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var o=this.consumeCodePoint();if(42===o&&47===(o=this.consumeCodePoint()))return this.consumeToken();if(-1===o)return this.consumeToken()}break;case 58:return jA;case 59:return NA;case 60:if(33===this.peekCodePoint(0)&&45===this.peekCodePoint(1)&&45===this.peekCodePoint(2))return this.consumeCodePoint(),this.consumeCodePoint(),MA;break;case 64:var i=this.peekCodePoint(0),s=this.peekCodePoint(1),a=this.peekCodePoint(2);if(QA(i,s,a))return{type:7,value:this.consumeName()};break;case 91:return KA;case 92:if(CA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return _A;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),IA;break;case 123:return xA;case 125:return HA;case 117:case 85:return s=this.peekCodePoint(0),a=this.peekCodePoint(1),43!==s||!fA(a)&&63!==a||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),EA;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),bA;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),LA;break;case-1:return GA}return pA(A)?(this.consumeWhiteSpace(),kA):gA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):hA(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:l(A)}},RA.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},RA.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},RA.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},RA.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();fA(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(l.apply(void 0,A.map((function(A){return 63===A?48:A}))),16),end:parseInt(l.apply(void 0,A.map((function(A){return 63===A?70:A}))),16)};var n=parseInt(l.apply(void 0,A),16);if(45===this.peekCodePoint(0)&&fA(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var r=[];fA(e)&&r.length<6;)r.push(e),e=this.consumeCodePoint();return{type:30,start:n,end:parseInt(l.apply(void 0,r),16)}}return{type:30,start:n,end:n}},RA.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},RA.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),-1===this.peekCodePoint(0))return{type:22,value:""};var e,t=this.peekCodePoint(0);if(39===t||34===t)return 0===(t=this.consumeStringToken(this.consumeCodePoint())).type&&(this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),TA);for(;;){var n=this.consumeCodePoint();if(-1===n||41===n)return{type:22,value:l.apply(void 0,A)};if(pA(n))return this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:22,value:l.apply(void 0,A)}):(this.consumeBadUrlRemnants(),TA);if(34===n||39===n||40===n||0<=(e=n)&&e<=8||11===e||14<=e&&e<=31||127===e)return this.consumeBadUrlRemnants(),TA;if(92===n){if(!CA(n,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),TA;A.push(this.consumeEscapedCodePoint())}else A.push(n)}},RA.prototype.consumeWhiteSpace=function(){for(;pA(this.peekCodePoint(0));)this.consumeCodePoint()},RA.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||-1===A)return;CA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},RA.prototype.consumeStringSlice=function(A){for(var e="";0<A;){var t=Math.min(5e4,A);e+=l.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},RA.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var n,r=this._value[t];if(-1===r||void 0===r||r===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(10===r)return this._value.splice(0,t),OA;92!==r||-1!==(n=this._value[t+1])&&void 0!==n&&(10===n?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):CA(r,n)&&(e+=this.consumeStringSlice(t),e+=l(this.consumeEscapedCodePoint()),t=-1)),t++}},RA.prototype.consumeNumber=function(){var A=[],e=4;for(43!==(t=this.peekCodePoint(0))&&45!==t||A.push(this.consumeCodePoint());gA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());var t=this.peekCodePoint(0),n=this.peekCodePoint(1);if(46===t&&gA(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;gA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),n=this.peekCodePoint(1);var r=this.peekCodePoint(2);if((69===t||101===t)&&((43===n||45===n)&&gA(r)||gA(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;gA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[function(A){var e=0,t=1;43!==A[e]&&45!==A[e]||(45===A[e]&&(t=-1),e++);for(var n=[];gA(A[e]);)n.push(A[e++]);var r=n.length?parseInt(l.apply(void 0,n),10):0;46===A[e]&&e++;for(var o=[];gA(A[e]);)o.push(A[e++]);var i=o.length,s=i?parseInt(l.apply(void 0,o),10):0;69!==A[e]&&101!==A[e]||e++;var a=1;43!==A[e]&&45!==A[e]||(45===A[e]&&(a=-1),e++);for(var c=[];gA(A[e]);)c.push(A[e++]);var u=c.length?parseInt(l.apply(void 0,c),10):0;return t*(r+s*Math.pow(10,-i))*Math.pow(10,a*u)}(A),e]},RA.prototype.consumeNumericToken=function(){var A,e=(A=this.consumeNumber())[0],t=A[1],n=this.peekCodePoint(0);return QA(n,this.peekCodePoint(1),A=this.peekCodePoint(2))?{type:15,number:e,flags:t,unit:this.consumeName()}:37===n?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},RA.prototype.consumeEscapedCodePoint=function(){var A,e=this.consumeCodePoint();if(fA(e)){for(var t=l(e);fA(this.peekCodePoint(0))&&t.length<6;)t+=l(this.consumeCodePoint());pA(this.peekCodePoint(0))&&this.consumeCodePoint();var n=parseInt(t,16);return 0===n||55296<=(A=n)&&A<=57343||1114111<n?65533:n}return-1===e?65533:e},RA.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(wA(e))A+=l(e);else{if(!CA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=l(this.consumeEscapedCodePoint())}}},RA);function RA(){this._value=[]}var VA=(zA.create=function(A){var e=new PA;return e.write(A),new zA(e.read())},zA.parseValue=function(A){return zA.create(A).parseComponentValue()},zA.parseValues=function(A){return zA.create(A).parseComponentValues()},zA.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);for(var e=this.consumeComponentValue();31===(A=this.consumeToken()).type;);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},zA.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},zA.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},zA.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||ue(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},zA.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},zA.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?GA:A},zA.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},zA);function zA(A){this._tokens=A}function XA(A){return 15===A.type}function JA(A){return 17===A.type}function WA(A){return 20===A.type}function YA(A){return 0===A.type}function ZA(A,e){return WA(A)&&A.value===e}function qA(A){return 31!==A.type}function $A(A){return 31!==A.type&&4!==A.type}function Ae(A){var e=[],t=[];return A.forEach((function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)})),t.length&&e.push(t),e}function ee(A){return 17===A.type||15===A.type}function te(A){return 16===A.type||ee(A)}function ne(A){return 1<A.length?[A[0],A[1]]:[A[0]]}function re(A,e,t){var n=A[0];return A=A[1],[ge(n,e),ge(void 0!==A?A:n,t)]}function oe(A){return 15===A.type&&("deg"===A.unit||"grad"===A.unit||"rad"===A.unit||"turn"===A.unit)}function ie(A){switch(A.filter(WA).map((function(A){return A.value})).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[le,le];case"to top":case"bottom":return pe(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[le,Be];case"to right":case"left":return pe(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[Be,Be];case"to bottom":case"top":return pe(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[Be,le];case"to left":case"right":return pe(270)}return 0}function se(A){return!(255&A)}function ae(A){var e=255&A,t=255&A>>8,n=255&A>>16;return A=255&A>>24,e<255?"rgba("+A+","+n+","+t+","+e/255+")":"rgb("+A+","+n+","+t+")"}function ce(A,e){if(17===A.type)return A.number;if(16!==A.type)return 0;var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}var ue=function(A,e){return 11===e&&12===A.type||28===e&&29===A.type||2===e&&3===A.type},le={type:17,number:0,flags:4},de={type:16,number:50,flags:4},Be={type:16,number:100,flags:4},ge=function(A,e){if(16===A.type)return A.number/100*e;if(XA(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},fe=function(A,e){if(15===e.type)switch(e.unit){case"deg":return Math.PI*e.number/180;case"grad":return Math.PI/200*e.number;case"rad":return e.number;case"turn":return 2*Math.PI*e.number}throw new Error("Unsupported angle type")},pe=function(A){return Math.PI*A/180},he=function(A,e){if(18===e.type){var t=Ee[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var n=e.value.substring(0,1),r=e.value.substring(1,2),o=e.value.substring(2,3);return we(parseInt(n+n,16),parseInt(r+r,16),parseInt(o+o,16),1)}if(4===e.value.length){n=e.value.substring(0,1),r=e.value.substring(1,2),o=e.value.substring(2,3);var i=e.value.substring(3,4);return we(parseInt(n+n,16),parseInt(r+r,16),parseInt(o+o,16),parseInt(i+i,16)/255)}if(6===e.value.length)return n=e.value.substring(0,2),r=e.value.substring(2,4),o=e.value.substring(4,6),we(parseInt(n,16),parseInt(r,16),parseInt(o,16),1);if(8===e.value.length)return n=e.value.substring(0,2),r=e.value.substring(2,4),o=e.value.substring(4,6),i=e.value.substring(6,8),we(parseInt(n,16),parseInt(r,16),parseInt(o,16),parseInt(i,16)/255)}return 20===e.type&&void 0!==(e=Le[e.value.toUpperCase()])?e:Le.TRANSPARENT},we=function(A,e,t,n){return(A<<24|e<<16|t<<8|Math.round(255*n))>>>0},Ce=function(A,e){if(3===(e=e.filter($A)).length){var t=(r=e.map(ce))[0],n=r[1],r=r[2];return we(t,n,r,1)}return 4!==e.length?0:(t=(e=e.map(ce))[0],n=e[1],r=e[2],e=e[3],we(t,n,r,e))};function Qe(A,e,t){return t<0&&(t+=1),1<=t&&--t,t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}function me(A,e){return he(A,VA.create(e).parseComponentValue())}function Ue(A,e){return A=he(A,e[0]),(e=e[1])&&te(e)?{color:A,stop:e}:{color:A,stop:null}}function ve(A,e){var t=A[0],n=A[A.length-1];null===t.stop&&(t.stop=le),null===n.stop&&(n.stop=Be);for(var r=[],o=0,i=0;i<A.length;i++){var s=A[i].stop;null!==s?(o<(s=ge(s,e))?r.push(s):r.push(o),o=s):r.push(null)}var a=null;for(i=0;i<r.length;i++){var c=r[i];if(null===c)null===a&&(a=i);else if(null!==a){for(var u=i-a,l=(c-r[a-1])/(1+u),d=1;d<=u;d++)r[a+d-1]=l*d;a=null}}return A.map((function(A,t){return{color:A.color,stop:Math.max(Math.min(1,r[t]/e),0)}}))}function ye(A,e){return Math.sqrt(A*A+e*e)}function Fe(A,e,t,n,r){return[[0,0],[0,e],[A,0],[A,e]].reduce((function(A,e){var o=e[0],i=e[1];return i=ye(t-o,n-i),(r?i<A.optimumDistance:i>A.optimumDistance)?{optimumCorner:e,optimumDistance:i}:A}),{optimumDistance:r?1/0:-1/0,optimumCorner:null}).optimumCorner}var Ie,be=function(A,e){var t=(o=e.filter($A))[0],n=o[1],r=o[2],o=(e=o[3],(17===t.type?pe(t.number):fe(0,t))/(2*Math.PI));return A=te(n)?n.number/100:0,t=te(r)?r.number/100:0,n=void 0!==e&&te(e)?ge(e,1):1,0==A?we(255*t,255*t,255*t,1):(A=Qe(e=2*t-(r=t<=.5?t*(1+A):t+A-t*A),r,o+1/3),t=Qe(e,r,o),o=Qe(e,r,o-1/3),we(255*A,255*t,255*o,n))},Ee={hsl:be,hsla:be,rgb:Ce,rgba:Ce},Le={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},xe={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(WA(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},He={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Se=(be=function(A,e){var t=pe(180),n=[];return Ae(e).forEach((function(e,r){if(0===r){if(20===(r=e[0]).type&&-1!==["top","left","right","bottom"].indexOf(r.value))return void(t=ie(e));if(oe(r))return void(t=(fe(0,r)+pe(270))%pe(360))}e=Ue(A,e),n.push(e)})),{angle:t,stops:n,type:1}},"closest-side"),Te="farthest-side",Oe="closest-corner",Me="farthest-corner",De="ellipse",je="contain",Ne=(Ce=function(A,e){var t=0,n=3,r=[],o=[];return Ae(e).forEach((function(e,i){var s=!0;0===i?s=e.reduce((function(A,e){if(WA(e))switch(e.value){case"center":return o.push(de),!1;case"top":case"left":return o.push(le),!1;case"right":case"bottom":return o.push(Be),!1}else if(te(e)||ee(e))return o.push(e),!1;return A}),s):1===i&&(s=e.reduce((function(A,e){if(WA(e))switch(e.value){case"circle":return t=0,!1;case De:return!(t=1);case je:case Se:return n=0,!1;case Te:return!(n=1);case Oe:return!(n=2);case"cover":case Me:return!(n=3)}else if(ee(e)||te(e))return(n=Array.isArray(n)?n:[]).push(e),!1;return A}),s)),s&&(e=Ue(A,e),r.push(e))})),{size:n,shape:t,stops:r,position:o,type:2}},function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18!==e.type)throw new Error("Unsupported image type "+e.type);if(void 0===(t=Ke[e.name]))throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return t(A,e.values)}),Ke={"linear-gradient":function(A,e){var t=pe(180),n=[];return Ae(e).forEach((function(e,r){if(0===r){if(20===(r=e[0]).type&&"to"===r.value)return void(t=ie(e));if(oe(r))return void(t=fe(0,r))}e=Ue(A,e),n.push(e)})),{angle:t,stops:n,type:1}},"-moz-linear-gradient":be,"-ms-linear-gradient":be,"-o-linear-gradient":be,"-webkit-linear-gradient":be,"radial-gradient":function(A,e){var t=0,n=3,r=[],o=[];return Ae(e).forEach((function(e,i){var s,a=!0;0===i&&(s=!1,a=e.reduce((function(A,e){if(s)if(WA(e))switch(e.value){case"center":return o.push(de),A;case"top":case"left":return o.push(le),A;case"right":case"bottom":return o.push(Be),A}else(te(e)||ee(e))&&o.push(e);else if(WA(e))switch(e.value){case"circle":return t=0,!1;case De:return!(t=1);case"at":return!(s=!0);case Se:return n=0,!1;case"cover":case Te:return!(n=1);case je:case Oe:return!(n=2);case Me:return!(n=3)}else if(ee(e)||te(e))return(n=Array.isArray(n)?n:[]).push(e),!1;return A}),a)),a&&(e=Ue(A,e),r.push(e))})),{size:n,shape:t,stops:r,position:o,type:2}},"-moz-radial-gradient":Ce,"-ms-radial-gradient":Ce,"-o-radial-gradient":Ce,"-webkit-radial-gradient":Ce,"-webkit-gradient":function(A,e){var t=pe(180),n=[],r=1;return Ae(e).forEach((function(e,t){var o;if(e=e[0],0===t){if(WA(e)&&"linear"===e.value)return void(r=1);if(WA(e)&&"radial"===e.value)return void(r=2)}18===e.type&&("from"===e.name?(o=he(A,e.values[0]),n.push({stop:le,color:o})):"to"===e.name?(o=he(A,e.values[0]),n.push({stop:Be,color:o})):"color-stop"!==e.name||2===(e=e.values.filter($A)).length&&(o=he(A,e[1]),JA(e=e[0])&&n.push({stop:{type:16,number:100*e.number,flags:e.flags},color:o})))})),1===r?{angle:(t+pe(180))%pe(360),stops:n,type:r}:{size:3,shape:0,stops:n,position:[],type:r}}},_e={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e.filter((function(A){return $A(A)&&!(20===A.type&&"none"===A.value||18===A.type&&!Ke[A.name])})).map((function(e){return Ne(A,e)}))}},ke={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(WA(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},Ge={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return Ae(e).map((function(A){return A.filter(te)})).map(ne)}},Pe={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return Ae(e).map((function(A){return A.filter(WA).map((function(A){return A.value})).join(" ")})).map(Re)}},Re=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};function Ve(A,e){return WA(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:te(A)?ge(A,e):e}(Ce=Ie=Ie||{}).AUTO="auto",Ce.CONTAIN="contain";var ze,Xe,Je={name:"background-size",initialValue:"0",prefix:!(Ce.COVER="cover"),type:1,parse:function(A,e){return Ae(e).map((function(A){return A.filter(We)}))}},We=function(A){return WA(A)||te(A)},Ye=(Ce=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Ce("top")),Ze=Ce("right"),qe=Ce("bottom"),$e=Ce("left"),At=(Ce=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return ne(e.filter(te))}}},Ce("top-left")),et=Ce("top-right"),tt=Ce("bottom-right"),nt=Ce("bottom-left"),rt=(Ce=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},Ce("top")),ot=Ce("right"),it=Ce("bottom"),st=Ce("left"),at=(Ce=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return XA(e)?e.number:0}}},Ce("top")),ct=Ce("right"),ut=Ce("bottom"),lt=Ce("left"),dt={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Bt={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"!==e?0:1}},gt={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(WA).reduce((function(A,e){return A|ft(e.value)}),0)}},ft=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},pt={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},ht={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return 20===e.type&&"normal"===e.value||17!==e.type&&15!==e.type?0:e.number}},wt={name:"line-break",initialValue:(Ce=ze=ze||{}).NORMAL="normal",prefix:!(Ce.STRICT="strict"),type:2,parse:function(A,e){return"strict"!==e?ze.NORMAL:ze.STRICT}},Ct={name:"line-height",initialValue:"normal",prefix:!1,type:4},Qt={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:Ne(A,e)}},mt={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"!==e?1:0}},Ut={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},vt=(Ce=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},Ce("top")),yt=Ce("right"),Ft=Ce("bottom"),It=Ce("left"),bt={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(WA).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}}))}},Et={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"!==e?"normal":"break-word"}},Lt=(Ce=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Ce("top")),xt=Ce("right"),Ht=Ce("bottom"),St=Ce("left"),Tt={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},Ot={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Mt={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&ZA(e[0],"none")?[]:Ae(e).map((function(e){for(var t={color:Le.TRANSPARENT,offsetX:le,offsetY:le,blur:le},n=0,r=0;r<e.length;r++){var o=e[r];ee(o)?(0===n?t.offsetX=o:1===n?t.offsetY=o:t.blur=o,n++):t.color=he(A,o)}return t}))}},Dt={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},jt={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18!==e.type)return null;var t=Nt[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}},Nt={matrix:function(A){return 6===(A=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}))).length?A:null},matrix3d:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number})),t=e[0],n=e[1];e[2],e[3];var r=e[4],o=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var i=e[12];return A=e[13],e[14],e[15],16===e.length?[t,n,r,o,i,A]:null}},Kt=[Ce={type:16,number:50,flags:4},Ce],_t={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){return 2!==(e=e.filter(te)).length?Kt:[e[0],e[1]]}},kt={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};function Gt(A,e){return!!(A&e)}function Pt(A,e,t){return(A=A&&A[Math.min(e,A.length-1)])?t?A.open:A.close:""}(Ce=Xe=Xe||{}).NORMAL="normal",Ce.BREAK_ALL="break-all";var Rt={name:"word-break",initialValue:"normal",prefix:!(Ce.KEEP_ALL="keep-all"),type:2,parse:function(A,e){switch(e){case"break-all":return Xe.BREAK_ALL;case"keep-all":return Xe.KEEP_ALL;default:return Xe.NORMAL}}},Vt={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(JA(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},zt=function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")},Xt={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return JA(e)?e.number:1}},Jt={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Wt={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(WA).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},Yt={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],n=[];return e.forEach((function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:n.push(t.join(" ")),t.length=0}})),t.length&&n.push(t.join(" ")),n.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},Zt={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},qt={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return JA(e)?e.number:WA(e)&&"bold"===e.value?700:400}},$t={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(WA).map((function(A){return A.value}))}},An={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},en={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},tn={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var n=[],r=e.filter(qA),o=0;o<r.length;o++){var i=r[o],s=r[o+1];20===i.type&&(s=s&&JA(s)?s.number:1,n.push({counter:i.value,increment:s}))}return n}},nn={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],n=e.filter(qA),r=0;r<n.length;r++){var o=n[r],i=n[r+1];WA(o)&&"none"!==o.value&&(i=i&&JA(i)?i.number:0,t.push({counter:o.value,reset:i}))}return t}},rn={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,e){return e.filter(XA).map((function(A){return zt(0,A)}))}},on={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var n=[],r=e.filter(YA);if(r.length%2!=0)return null;for(var o=0;o<r.length;o+=2){var i=r[o].value,s=r[o+1].value;n.push({open:i,close:s})}return n}},sn={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&ZA(e[0],"none")?[]:Ae(e).map((function(e){for(var t={color:255,offsetX:le,offsetY:le,blur:le,spread:le,inset:!1},n=0,r=0;r<e.length;r++){var o=e[r];ZA(o,"inset")?t.inset=!0:ee(o)?(0===n?t.offsetX=o:1===n?t.offsetY=o:2===n?t.blur=o:t.spread=o,n++):t.color=he(A,o)}return t}))}},an={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[];return e.filter(WA).forEach((function(A){switch(A.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2)}})),[0,1,2].forEach((function(A){-1===t.indexOf(A)&&t.push(A)})),t}},cn={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},un={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return XA(e)?e.number:0}},ln=(dn.prototype.isVisible=function(){return 0<this.display&&0<this.opacity&&0===this.visibility},dn.prototype.isTransparent=function(){return se(this.backgroundColor)},dn.prototype.isTransformed=function(){return null!==this.transform},dn.prototype.isPositioned=function(){return 0!==this.position},dn.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},dn.prototype.isFloating=function(){return 0!==this.float},dn.prototype.isInlineLevel=function(){return Gt(this.display,4)||Gt(this.display,33554432)||Gt(this.display,268435456)||Gt(this.display,536870912)||Gt(this.display,67108864)||Gt(this.display,134217728)},dn);function dn(A,e){this.animationDuration=fn(A,rn,e.animationDuration),this.backgroundClip=fn(A,xe,e.backgroundClip),this.backgroundColor=fn(A,He,e.backgroundColor),this.backgroundImage=fn(A,_e,e.backgroundImage),this.backgroundOrigin=fn(A,ke,e.backgroundOrigin),this.backgroundPosition=fn(A,Ge,e.backgroundPosition),this.backgroundRepeat=fn(A,Pe,e.backgroundRepeat),this.backgroundSize=fn(A,Je,e.backgroundSize),this.borderTopColor=fn(A,Ye,e.borderTopColor),this.borderRightColor=fn(A,Ze,e.borderRightColor),this.borderBottomColor=fn(A,qe,e.borderBottomColor),this.borderLeftColor=fn(A,$e,e.borderLeftColor),this.borderTopLeftRadius=fn(A,At,e.borderTopLeftRadius),this.borderTopRightRadius=fn(A,et,e.borderTopRightRadius),this.borderBottomRightRadius=fn(A,tt,e.borderBottomRightRadius),this.borderBottomLeftRadius=fn(A,nt,e.borderBottomLeftRadius),this.borderTopStyle=fn(A,rt,e.borderTopStyle),this.borderRightStyle=fn(A,ot,e.borderRightStyle),this.borderBottomStyle=fn(A,it,e.borderBottomStyle),this.borderLeftStyle=fn(A,st,e.borderLeftStyle),this.borderTopWidth=fn(A,at,e.borderTopWidth),this.borderRightWidth=fn(A,ct,e.borderRightWidth),this.borderBottomWidth=fn(A,ut,e.borderBottomWidth),this.borderLeftWidth=fn(A,lt,e.borderLeftWidth),this.boxShadow=fn(A,sn,e.boxShadow),this.color=fn(A,dt,e.color),this.direction=fn(A,Bt,e.direction),this.display=fn(A,gt,e.display),this.float=fn(A,pt,e.cssFloat),this.fontFamily=fn(A,Yt,e.fontFamily),this.fontSize=fn(A,Zt,e.fontSize),this.fontStyle=fn(A,An,e.fontStyle),this.fontVariant=fn(A,$t,e.fontVariant),this.fontWeight=fn(A,qt,e.fontWeight),this.letterSpacing=fn(A,ht,e.letterSpacing),this.lineBreak=fn(A,wt,e.lineBreak),this.lineHeight=fn(A,Ct,e.lineHeight),this.listStyleImage=fn(A,Qt,e.listStyleImage),this.listStylePosition=fn(A,mt,e.listStylePosition),this.listStyleType=fn(A,Ut,e.listStyleType),this.marginTop=fn(A,vt,e.marginTop),this.marginRight=fn(A,yt,e.marginRight),this.marginBottom=fn(A,Ft,e.marginBottom),this.marginLeft=fn(A,It,e.marginLeft),this.opacity=fn(A,Xt,e.opacity);var t=fn(A,bt,e.overflow);this.overflowX=t[0],this.overflowY=t[1<t.length?1:0],this.overflowWrap=fn(A,Et,e.overflowWrap),this.paddingTop=fn(A,Lt,e.paddingTop),this.paddingRight=fn(A,xt,e.paddingRight),this.paddingBottom=fn(A,Ht,e.paddingBottom),this.paddingLeft=fn(A,St,e.paddingLeft),this.paintOrder=fn(A,an,e.paintOrder),this.position=fn(A,Ot,e.position),this.textAlign=fn(A,Tt,e.textAlign),this.textDecorationColor=fn(A,Jt,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=fn(A,Wt,null!==(t=e.textDecorationLine)&&void 0!==t?t:e.textDecoration),this.textShadow=fn(A,Mt,e.textShadow),this.textTransform=fn(A,Dt,e.textTransform),this.transform=fn(A,jt,e.transform),this.transformOrigin=fn(A,_t,e.transformOrigin),this.visibility=fn(A,kt,e.visibility),this.webkitTextStrokeColor=fn(A,cn,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=fn(A,un,e.webkitTextStrokeWidth),this.wordBreak=fn(A,Rt,e.wordBreak),this.zIndex=fn(A,Vt,e.zIndex)}for(var Bn=function(A,e){this.content=fn(A,en,e.content),this.quotes=fn(A,on,e.quotes)},gn=function(A,e){this.counterIncrement=fn(A,tn,e.counterIncrement),this.counterReset=fn(A,nn,e.counterReset)},fn=function(A,e,t){var n=new PA;t=null!=t?t.toString():e.initialValue,n.write(t);var r=new VA(n.read());switch(e.type){case 2:var o=r.parseComponentValue();return e.parse(A,WA(o)?o.value:e.initialValue);case 0:return e.parse(A,r.parseComponentValue());case 1:return e.parse(A,r.parseComponentValues());case 4:return r.parseComponentValue();case 3:switch(e.format){case"angle":return fe(0,r.parseComponentValue());case"color":return he(A,r.parseComponentValue());case"image":return Ne(A,r.parseComponentValue());case"length":var i=r.parseComponentValue();return ee(i)?i:le;case"length-percentage":return te(i=r.parseComponentValue())?i:le;case"time":return zt(0,r.parseComponentValue())}}},pn=function(A,e){return 1===(A=function(A){switch(A.getAttribute("data-html2canvas-debug")){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}}(A))||e===A},hn=function(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,pn(e,3),this.styles=new ln(A,window.getComputedStyle(e,null)),kr(e)&&(this.styles.animationDuration.some((function(A){return 0<A}))&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=c(this.context,e),pn(e,4)&&(this.flags|=16)},wn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Cn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Qn=0;Qn<64;Qn++)Cn[wn.charCodeAt(Qn)]=Qn;function mn(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))}var Un=(vn.prototype.get=function(A){var e;if(0<=A){if(A<55296||56319<A&&A<=65535)return e=this.index[A>>5],this.data[e=(e<<2)+(31&A)];if(A<=65535)return e=this.index[2048+(A-55296>>5)],this.data[e=(e<<2)+(31&A)];if(A<this.highStart)return e=this.index[e=2080+(A>>11)],e=this.index[e+=A>>5&63],this.data[e=(e<<2)+(31&A)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},vn);function vn(A,e,t,n,r,o){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=n,this.index=r,this.data=o}for(var yn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Fn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),In=0;In<64;In++)Fn[yn.charCodeAt(In)]=In;function bn(A){return Kn.get(A)}function En(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]}var Ln,xn,Hn,Sn,Tn,On,Mn=8,Dn=9,jn=11,Nn=12,Kn=(Hn=function(A){var e,t,n,r,o=.75*A.length,i=A.length,s=0;"="===A[A.length-1]&&(o--,"="===A[A.length-2]&&o--),o=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(o);for(var a=Array.isArray(o)?o:new Uint8Array(o),c=0;c<i;c+=4)e=Cn[A.charCodeAt(c)],t=Cn[A.charCodeAt(c+1)],n=Cn[A.charCodeAt(c+2)],r=Cn[A.charCodeAt(c+3)],a[s++]=e<<2|t>>4,a[s++]=(15&t)<<4|n>>2,a[s++]=(3&n)<<6|63&r;return o}("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"),Sn=Array.isArray(Hn)?function(A){for(var e=A.length,t=[],n=0;n<e;n+=4)t.push(A[n+3]<<24|A[n+2]<<16|A[n+1]<<8|A[n]);return t}(Hn):new Uint32Array(Hn),Tn=Array.isArray(Hn)?function(A){for(var e=A.length,t=[],n=0;n<e;n+=2)t.push(A[n+1]<<8|A[n]);return t}(Hn):new Uint16Array(Hn),Ln=mn(Tn,12,Sn[4]/2),xn=2===Sn[5]?mn(Tn,(24+Sn[4])/2):(Hn=Sn,Tn=Math.ceil((24+Sn[4])/4),Hn.slice?Hn.slice(Tn,xn):new Uint32Array(Array.prototype.slice.call(Hn,Tn,xn))),new Un(Sn[0],Sn[1],Sn[2],Sn[3],Ln,xn)),_n="×",kn=function(A,e,t,n,r){var o="http://www.w3.org/2000/svg",i=document.createElementNS(o,"svg");return o=document.createElementNS(o,"foreignObject"),i.setAttributeNS(null,"width",A.toString()),i.setAttributeNS(null,"height",e.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",t.toString()),o.setAttributeNS(null,"y",n.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),i.appendChild(o),o.appendChild(r),i},Gn=function(A){return new Promise((function(e,t){var n=new Image;n.onload=function(){return e(n)},n.onerror=t,n.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Pn={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");if(t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t),e=e.getBoundingClientRect(),e=Math.round(e.height),A.body.removeChild(t),123===e)return!0}}return!1}(document);return Object.defineProperty(Pn,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=Pn.SUPPORT_RANGE_BOUNDS&&function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var t=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var n=e.firstChild,r=u(n.data).map((function(A){return l(A)})),o=0,i={};return r=r.every((function(A,e){t.setStart(n,o),t.setEnd(n,o+A.length);var r=t.getBoundingClientRect();return o+=A.length,A=r.x>i.x||r.y>i.y,i=r,0===e||A})),A.body.removeChild(e),r}(document);return Object.defineProperty(Pn,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas");if(!(A=t.getContext("2d")))return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{A.drawImage(e,0,0),t.toDataURL()}catch(A){return!1}return!0}(document);return Object.defineProperty(Pn,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var n=e.getContext("2d");if(!n)return Promise.reject(!1);n.fillStyle="rgb(0, 255, 0)",n.fillRect(0,0,t,t);var r=new Image,o=e.toDataURL();return r.src=o,r=kn(t,t,0,0,r),n.fillStyle="red",n.fillRect(0,0,t,t),Gn(r).then((function(e){n.drawImage(e,0,0);var r=n.getImageData(0,0,t,t).data;return n.fillStyle="red",n.fillRect(0,0,t,t),(e=A.createElement("div")).style.backgroundImage="url("+o+")",e.style.height="100px",En(r)?Gn(kn(t,t,0,0,e)):Promise.reject(!1)})).then((function(A){return n.drawImage(A,0,0),En(n.getImageData(0,0,t,t).data)})).catch((function(){return!1}))}(document):Promise.resolve(!1);return Object.defineProperty(Pn,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(Pn,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(Pn,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Pn,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(Pn,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},Rn=function(A,e){this.text=A,this.bounds=e},Vn=function(A,e,t){var n=A.ownerDocument;if(!n)throw new Error("Node has no owner document");return(n=n.createRange()).setStart(A,e),n.setEnd(A,e+t),n},zn=function(A){if(Pn.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return function(A){for(var e,t=function(A){var e=function(A){for(var e=[],t=0,n=A.length;t<n;){var r,o=A.charCodeAt(t++);55296<=o&&o<=56319&&t<n?56320==(64512&(r=A.charCodeAt(t++)))?e.push(((1023&o)<<10)+(1023&r)+65536):(e.push(o),t--):e.push(o)}return e}(A),t=e.length,n=0,r=0,o=e.map(bn);return{next:function(){if(t<=n)return{done:!0,value:null};for(var A=_n;n<t&&(A=function(A,e){var t=e-2,n=A[t],r=A[e-1];if(e=A[e],2===r&&3===e)return _n;if(2===r||3===r||4===r)return"÷";if(2===e||3===e||4===e)return"÷";if(r===Mn&&-1!==[Mn,Dn,jn,Nn].indexOf(e))return _n;if(!(r!==jn&&r!==Dn||e!==Dn&&10!==e))return _n;if((r===Nn||10===r)&&10===e)return _n;if(13===e||5===e)return _n;if(7===e)return _n;if(1===r)return _n;if(13===r&&14===e){for(;5===n;)n=A[--t];if(14===n)return _n}if(15===r&&15===e){for(var o=0;15===n;)o++,n=A[--t];if(o%2==0)return _n}return"÷"}(o,++n))===_n;);if(A===_n&&n!==t)return{done:!0,value:null};var i=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var n=[],r=-1,o="";++r<t;){var i=A[r];i<=65535?n.push(i):(i-=65536,n.push(55296+(i>>10),i%1024+56320)),(r+1===t||16384<n.length)&&(o+=String.fromCharCode.apply(String,n),n.length=0)}return o}.apply(null,e.slice(r,n));return r=n,{value:i,done:!1}}}}(A),n=[];!(e=t.next()).done;)e.value&&n.push(e.value.slice());return n}(A)},Xn=[32,160,4961,65792,65793,4153,4241],Jn=function(A,e){for(var t,n=BA(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),r=[];!(t=n.next()).done;)!function(){var A,e;t.value&&(A=t.value.slice(),A=u(A),e="",A.forEach((function(A){-1===Xn.indexOf(A)?e+=l(A):(e.length&&r.push(e),r.push(l(A)),e="")})),e.length&&r.push(e))}();return r},Wn=function(A,e,t){var n,r,o,i,a;this.text=Yn(e.data,t.textTransform),this.textBounds=(n=A,A=this.text,o=e,A=function(A,e){return 0!==e.letterSpacing?zn(A):function(A,e){if(Pn.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return Jn(A,e)}(A,e)}(A,r=t),i=[],a=0,A.forEach((function(A){var e,t,u;r.textDecorationLine.length||0<A.trim().length?Pn.SUPPORT_RANGE_BOUNDS?1<(u=Vn(o,a,A.length).getClientRects()).length?(e=zn(A),t=0,e.forEach((function(A){i.push(new Rn(A,s.fromDOMRectList(n,Vn(o,t+a,A.length).getClientRects()))),t+=A.length}))):i.push(new Rn(A,s.fromDOMRectList(n,u))):(u=o.splitText(A.length),i.push(new Rn(A,function(A,e){var t=e.ownerDocument;if(t){var n=t.createElement("html2canvaswrapper");if(n.appendChild(e.cloneNode(!0)),t=e.parentNode)return t.replaceChild(n,e),A=c(A,n),n.firstChild&&t.replaceChild(n.firstChild,n),A}return s.EMPTY}(n,o))),o=u):Pn.SUPPORT_RANGE_BOUNDS||(o=o.splitText(A.length)),a+=A.length})),i)},Yn=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(Zn,qn);case 2:return A.toUpperCase();default:return A}},Zn=/(^|\s|:|-|\(|\))([a-z])/g,qn=function(A,e,t){return 0<A.length?e+t.toUpperCase():A},$n=(e(Ar,On=hn),Ar);function Ar(A,e){return(A=On.call(this,A,e)||this).src=e.currentSrc||e.src,A.intrinsicWidth=e.naturalWidth,A.intrinsicHeight=e.naturalHeight,A.context.cache.addImage(A.src),A}var er,tr=(e(nr,er=hn),nr);function nr(A,e){return(A=er.call(this,A,e)||this).canvas=e,A.intrinsicWidth=e.width,A.intrinsicHeight=e.height,A}var rr,or=(e(ir,rr=hn),ir);function ir(A,e){var t=rr.call(this,A,e)||this,n=new XMLSerializer;return A=c(A,e),e.setAttribute("width",A.width+"px"),e.setAttribute("height",A.height+"px"),t.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(e)),t.intrinsicWidth=e.width.baseVal.value,t.intrinsicHeight=e.height.baseVal.value,t.context.cache.addImage(t.svg),t}var sr,ar=(e(cr,sr=hn),cr);function cr(A,e){return(A=sr.call(this,A,e)||this).value=e.value,A}var ur,lr=(e(dr,ur=hn),dr);function dr(A,e){return(A=ur.call(this,A,e)||this).start=e.start,A.reversed="boolean"==typeof e.reversed&&!0===e.reversed,A}var Br,gr=[{type:15,flags:0,unit:"px",number:3}],fr=[{type:16,flags:0,number:50}],pr="checkbox",hr="radio",wr=707406591,Cr=(e(Qr,Br=hn),Qr);function Qr(A,e){var t=Br.call(this,A,e)||this;switch(t.type=e.type.toLowerCase(),t.checked=e.checked,t.value=0===(e="password"===(A=e).type?new Array(A.value.length+1).join("•"):A.value).length?A.placeholder||"":e,t.type!==pr&&t.type!==hr||(t.styles.backgroundColor=3739148031,t.styles.borderTopColor=t.styles.borderRightColor=t.styles.borderBottomColor=t.styles.borderLeftColor=2779096575,t.styles.borderTopWidth=t.styles.borderRightWidth=t.styles.borderBottomWidth=t.styles.borderLeftWidth=1,t.styles.borderTopStyle=t.styles.borderRightStyle=t.styles.borderBottomStyle=t.styles.borderLeftStyle=1,t.styles.backgroundClip=[0],t.styles.backgroundOrigin=[0],t.bounds=(e=t.bounds).width>e.height?new s(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new s(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e),t.type){case pr:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=gr;break;case hr:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=fr}return t}var mr,Ur=(e(vr,mr=hn),vr);function vr(A,e){return A=mr.call(this,A,e)||this,e=e.options[e.selectedIndex||0],A.value=e&&e.text||"",A}var yr,Fr=(e(Ir,yr=hn),Ir);function Ir(A,e){return(A=yr.call(this,A,e)||this).value=e.value,A}var br,Er=(e(Lr,br=hn),Lr);function Lr(A,e){var t,n,r=br.call(this,A,e)||this;r.src=e.src,r.width=parseInt(e.width,10)||0,r.height=parseInt(e.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{e.contentWindow&&e.contentWindow.document&&e.contentWindow.document.documentElement&&(r.tree=Dr(A,e.contentWindow.document.documentElement),t=e.contentWindow.document.documentElement?me(A,getComputedStyle(e.contentWindow.document.documentElement).backgroundColor):Le.TRANSPARENT,n=e.contentWindow.document.body?me(A,getComputedStyle(e.contentWindow.document.body).backgroundColor):Le.TRANSPARENT,r.backgroundColor=se(t)?se(n)?r.styles.backgroundColor:n:t)}catch(A){}return r}function xr(A){return"VIDEO"===A.tagName}function Hr(A){return"STYLE"===A.tagName}function Sr(A){return 0<A.tagName.indexOf("-")}var Tr=["OL","UL","MENU"],Or=function(A,e,t,n){for(var r=e.firstChild;r;r=i){var o,i=r.nextSibling;Kr(r)&&0<r.data.trim().length?t.textNodes.push(new Wn(A,r,t.styles)):_r(r)&&($r(r)&&r.assignedNodes?r.assignedNodes().forEach((function(e){return Or(A,e,t,n)})):(o=Mr(A,r)).styles.isVisible()&&(jr(r,o,n)?o.flags|=4:Nr(o.styles)&&(o.flags|=2),-1!==Tr.indexOf(r.tagName)&&(o.flags|=8),t.elements.push(o),r.slot,r.shadowRoot?Or(A,r.shadowRoot,o,n):Zr(r)||zr(r)||qr(r)||Or(A,r,o,n)))}},Mr=function(A,e){return new(Wr(e)?$n:Jr(e)?tr:zr(e)?or:Pr(e)?ar:Rr(e)?lr:Vr(e)?Cr:qr(e)?Ur:Zr(e)?Fr:Yr(e)?Er:hn)(A,e)},Dr=function(A,e){var t=Mr(A,e);return t.flags|=4,Or(A,e,t,t),t},jr=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||Xr(A)&&t.styles.isTransparent()},Nr=function(A){return A.isPositioned()||A.isFloating()},Kr=function(A){return A.nodeType===Node.TEXT_NODE},_r=function(A){return A.nodeType===Node.ELEMENT_NODE},kr=function(A){return _r(A)&&void 0!==A.style&&!Gr(A)},Gr=function(A){return"object"==i(A.className)},Pr=function(A){return"LI"===A.tagName},Rr=function(A){return"OL"===A.tagName},Vr=function(A){return"INPUT"===A.tagName},zr=function(A){return"svg"===A.tagName},Xr=function(A){return"BODY"===A.tagName},Jr=function(A){return"CANVAS"===A.tagName},Wr=function(A){return"IMG"===A.tagName},Yr=function(A){return"IFRAME"===A.tagName},Zr=function(A){return"TEXTAREA"===A.tagName},qr=function(A){return"SELECT"===A.tagName},$r=function(A){return"SLOT"===A.tagName},Ao=(eo.prototype.getCounterValue=function(A){return(A=this.counters[A])&&A.length?A[A.length-1]:1},eo.prototype.getCounterValues=function(A){return(A=this.counters[A])||[]},eo.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},eo.prototype.parse=function(A){var e=this,t=A.counterIncrement,n=(A=A.counterReset,!0);null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&0!==A.increment&&(n=!1,t.length||t.push(1),t[Math.max(0,t.length-1)]+=A.increment)}));var r=[];return n&&A.forEach((function(A){var t=e.counters[A.counter];r.push(A.counter),(t=t||(e.counters[A.counter]=[])).push(A.reset)})),r},eo);function eo(){this.counters={}}function to(A,e,t,n,r,o){return A<e||t<A?go(A,r,0<o.length):n.integers.reduce((function(e,t,r){for(;t<=A;)A-=t,e+=n.values[r];return e}),"")+o}function no(A,e,t,n){for(var r="";t||A--,r=n(A)+r,e<=(A/=e)*e;);return r}function ro(A,e,t,n,r){var o=t-e+1;return(A<0?"-":"")+(no(Math.abs(A),o,n,(function(A){return l(Math.floor(A%o)+e)}))+r)}function oo(A,e,t){void 0===t&&(t=". ");var n=e.length;return no(Math.abs(A),n,!1,(function(A){return e[Math.floor(A%n)]}))+t}function io(A,e,t,n,r,o){if(A<-9999||9999<A)return go(A,4,0<r.length);var i=Math.abs(A),s=r;if(0===i)return e[0]+s;for(var a=0;0<i&&a<=4;a++){var c=i%10;0==c&&Gt(o,1)&&""!==s?s=e[c]+s:1<c||1==c&&0===a||1==c&&1===a&&Gt(o,2)||1==c&&1===a&&Gt(o,4)&&100<A||1==c&&1<a&&Gt(o,8)?s=e[c]+(0<a?t[a-1]:"")+s:1==c&&0<a&&(s=t[a-1]+s),i=Math.floor(i/10)}return(A<0?n:"")+s}var so,ao={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},co={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},uo={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},lo={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},Bo="마이너스",go=function(A,e,t){var n=t?". ":"",r=t?"、":"",o=t?", ":"",i=t?" ":"";switch(e){case 0:return"•"+i;case 1:return"◦"+i;case 2:return"◾"+i;case 5:var s=ro(A,48,57,!0,n);return s.length<4?"0"+s:s;case 4:return oo(A,"〇一二三四五六七八九",r);case 6:return to(A,1,3999,ao,3,n).toLowerCase();case 7:return to(A,1,3999,ao,3,n);case 8:return ro(A,945,969,!1,n);case 9:return ro(A,97,122,!1,n);case 10:return ro(A,65,90,!1,n);case 11:return ro(A,1632,1641,!0,n);case 12:case 49:return to(A,1,9999,co,3,n);case 35:return to(A,1,9999,co,3,n).toLowerCase();case 13:return ro(A,2534,2543,!0,n);case 14:case 30:return ro(A,6112,6121,!0,n);case 15:return oo(A,"子丑寅卯辰巳午未申酉戌亥",r);case 16:return oo(A,"甲乙丙丁戊己庚辛壬癸",r);case 17:case 48:return io(A,"零一二三四五六七八九","十百千萬","負",r,14);case 47:return io(A,"零壹貳參肆伍陸柒捌玖","拾佰仟萬","負",r,15);case 42:return io(A,"零一二三四五六七八九","十百千萬","负",r,14);case 41:return io(A,"零壹贰叁肆伍陆柒捌玖","拾佰仟萬","负",r,15);case 26:return io(A,"〇一二三四五六七八九","十百千万","マイナス",r,0);case 25:return io(A,"零壱弐参四伍六七八九","拾百千万","マイナス",r,7);case 31:return io(A,"영일이삼사오육칠팔구","십백천만",Bo,o,7);case 33:return io(A,"零一二三四五六七八九","十百千萬",Bo,o,0);case 32:return io(A,"零壹貳參四五六七八九","拾百千",Bo,o,7);case 18:return ro(A,2406,2415,!0,n);case 20:return to(A,1,19999,lo,3,n);case 21:return ro(A,2790,2799,!0,n);case 22:return ro(A,2662,2671,!0,n);case 22:return to(A,1,10999,uo,3,n);case 23:return oo(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return oo(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return ro(A,3302,3311,!0,n);case 28:return oo(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",r);case 29:return oo(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",r);case 34:return ro(A,3792,3801,!0,n);case 37:return ro(A,6160,6169,!0,n);case 38:return ro(A,4160,4169,!0,n);case 39:return ro(A,2918,2927,!0,n);case 40:return ro(A,1776,1785,!0,n);case 43:return ro(A,3046,3055,!0,n);case 44:return ro(A,3174,3183,!0,n);case 45:return ro(A,3664,3673,!0,n);case 46:return ro(A,3872,3881,!0,n);default:return ro(A,48,57,!0,n)}},fo="data-html2canvas-ignore",po=(ho.prototype.toIFrame=function(A,e){var t=this,o=Co(A,e);if(!o.contentWindow)return Promise.reject("Unable to find iframe window");var i=A.defaultView.pageXOffset,s=A.defaultView.pageYOffset,a=o.contentWindow,c=a.document;return A=mo(o).then((function(){return n(t,void 0,void 0,(function(){var A,t;return r(this,(function(n){switch(n.label){case 0:return this.scrolledElements.forEach(Io),a&&(a.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||a.scrollY===e.top&&a.scrollX===e.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(a.scrollX-e.left,a.scrollY-e.top,0,0))),A=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:c.fonts&&c.fonts.ready?[4,c.fonts.ready]:[3,2];case 1:n.sent(),n.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Qo(c)]:[3,4];case 3:n.sent(),n.label=4;case 4:return"function"==typeof A?[2,Promise.resolve().then((function(){return A(c,t)})).then((function(){return o}))]:[2,o]}}))}))})),c.open(),c.write(yo(document.doctype)+"<html></html>"),Fo(this.referenceElement.ownerDocument,i,s),c.replaceChild(c.adoptNode(this.documentElement),c.documentElement),c.close(),A},ho.prototype.createElementClone=function(A){if(pn(A,2),Jr(A))return this.createCanvasClone(A);if(xr(A))return this.createVideoClone(A);if(Hr(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return Wr(e)&&(Wr(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),Sr(e)?this.createCustomElementClone(e):e},ho.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return vo(A.style,e),e},ho.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A}),""),n=A.cloneNode(!1);return n.textContent=t,n}}catch(A){if(this.context.logger.error("Unable to access cssRules property",A),"SecurityError"!==A.name)throw A}return A.cloneNode(!1)},ho.prototype.createCanvasClone=function(A){var e;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(e){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}t=A.cloneNode(!1);try{t.width=A.width,t.height=A.height;var n,r,o=A.getContext("2d"),i=t.getContext("2d");return i&&(!this.options.allowTaint&&o?i.putImageData(o.getImageData(0,0,A.width,A.height),0,0):(!(n=null!==(e=A.getContext("webgl2"))&&void 0!==e?e:A.getContext("webgl"))||!1===(null==(r=n.getContextAttributes())?void 0:r.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A),i.drawImage(A,0,0))),t}catch(e){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return t},ho.prototype.createVideoClone=function(A){var e=A.ownerDocument.createElement("canvas");e.width=A.offsetWidth,e.height=A.offsetHeight;var t=e.getContext("2d");try{return t&&(t.drawImage(A,0,0,e.width,e.height),this.options.allowTaint||t.getImageData(0,0,e.width,e.height)),e}catch(e){this.context.logger.info("Unable to clone video as it is tainted",A)}return(e=A.ownerDocument.createElement("canvas")).width=A.offsetWidth,e.height=A.offsetHeight,e},ho.prototype.appendChildNode=function(A,e,t){_r(e)&&("SCRIPT"===e.tagName||e.hasAttribute(fo)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&_r(e)&&Hr(e)||A.appendChild(this.cloneNode(e,t))},ho.prototype.cloneChildNodes=function(A,e,t){for(var n,r=this,o=(A.shadowRoot||A).firstChild;o;o=o.nextSibling)_r(o)&&$r(o)&&"function"==typeof o.assignedNodes?(n=o.assignedNodes()).length&&n.forEach((function(A){return r.appendChildNode(e,A,t)})):this.appendChildNode(e,o,t)},ho.prototype.cloneNode=function(A,e){if(Kr(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&_r(A)&&(kr(A)||Gr(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var r=t.getComputedStyle(A),o=t.getComputedStyle(A,":before"),i=t.getComputedStyle(A,":after");return this.referenceElement===A&&kr(n)&&(this.clonedReferenceElement=n),Xr(n)&&xo(n),t=this.counters.parse(new gn(this.context,r)),o=this.resolvePseudoContent(A,n,o,so.BEFORE),Sr(A)&&(e=!0),xr(A)||this.cloneChildNodes(A,n,e),o&&n.insertBefore(o,n.firstChild),(i=this.resolvePseudoContent(A,n,i,so.AFTER))&&n.appendChild(i),this.counters.pop(t),(r&&(this.options.copyStyles||Gr(A))&&!Yr(A)||e)&&vo(r,n),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(Zr(A)||qr(A))&&(Zr(n)||qr(n))&&(n.value=A.value),n}return A.cloneNode(!1)},ho.prototype.resolvePseudoContent=function(A,e,t,n){var r=this;if(t){var o=t.content,i=e.ownerDocument;if(i&&o&&"none"!==o&&"-moz-alt-content"!==o&&"none"!==t.display){this.counters.parse(new gn(this.context,t));var s=new Bn(this.context,t),a=i.createElement("html2canvaspseudoelement");return vo(t,a),s.content.forEach((function(e){if(0===e.type)a.appendChild(i.createTextNode(e.value));else if(22===e.type){var t=i.createElement("img");t.src=e.value,t.style.opacity="1",a.appendChild(t)}else if(18===e.type){var n,o,c,u,l;"attr"===e.name?(t=e.values.filter(WA)).length&&a.appendChild(i.createTextNode(A.getAttribute(t[0].value)||"")):"counter"===e.name?(c=(o=e.values.filter($A))[0],o=o[1],c&&WA(c)&&(n=r.counters.getCounterValue(c.value),l=o&&WA(o)?Ut.parse(r.context,o.value):3,a.appendChild(i.createTextNode(go(n,l,!1))))):"counters"===e.name&&(c=(n=e.values.filter($A))[0],l=n[1],o=n[2],c&&WA(c)&&(c=r.counters.getCounterValues(c.value),u=o&&WA(o)?Ut.parse(r.context,o.value):3,l=l&&0===l.type?l.value:"",l=c.map((function(A){return go(A,u,!1)})).join(l),a.appendChild(i.createTextNode(l))))}else if(20===e.type)switch(e.value){case"open-quote":a.appendChild(i.createTextNode(Pt(s.quotes,r.quoteDepth++,!0)));break;case"close-quote":a.appendChild(i.createTextNode(Pt(s.quotes,--r.quoteDepth,!1)));break;default:a.appendChild(i.createTextNode(e.value))}})),a.className=bo+" "+Eo,n=n===so.BEFORE?" "+bo:" "+Eo,Gr(e)?e.className.baseValue+=n:e.className+=n,a}}},ho.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},ho);function ho(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new Ao,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}function wo(A){return new Promise((function(e){!A.complete&&A.src?(A.onload=e,A.onerror=e):e()}))}(Ce=so=so||{})[Ce.BEFORE=0]="BEFORE",Ce[Ce.AFTER=1]="AFTER";var Co=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(fo,"true"),A.body.appendChild(t),t},Qo=function(A){return Promise.all([].slice.call(A.images,0).map(wo))},mo=function(A){return new Promise((function(e,t){var n=A.contentWindow;if(!n)return t("No window assigned for iframe");var r=n.document;n.onload=A.onload=function(){n.onload=A.onload=null;var t=setInterval((function(){0<r.body.childNodes.length&&"complete"===r.readyState&&(clearInterval(t),e(A))}),50)}}))},Uo=["all","d","content"],vo=function(A,e){for(var t=A.length-1;0<=t;t--){var n=A.item(t);-1===Uo.indexOf(n)&&e.style.setProperty(n,A.getPropertyValue(n))}return e},yo=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},Fo=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},Io=function(A){var e=A[0],t=A[1];A=A[2],e.scrollLeft=t,e.scrollTop=A},bo="___html2canvas___pseudoelement_before",Eo="___html2canvas___pseudoelement_after",Lo='{\n    content: "" !important;\n    display: none !important;\n}',xo=function(A){Ho(A,"."+bo+":before"+Lo+"\n         ."+Eo+":after"+Lo)},Ho=function(A,e){var t=A.ownerDocument;t&&((t=t.createElement("style")).textContent=e,A.appendChild(t))},So=(To.getOrigin=function(A){var e=To._link;return e?(e.href=A,e.href=e.href,e.protocol+e.hostname+e.port):"about:blank"},To.isSameOrigin=function(A){return To.getOrigin(A)===To._origin},To.setContext=function(A){To._link=A.document.createElement("a"),To._origin=To.getOrigin(A.location.href)},To._origin="about:blank",To);function To(){}var Oo=(Mo.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)||(Go(A)||Ko(A))&&(this._cache[A]=this.loadImage(A)).catch((function(){})),e},Mo.prototype.match=function(A){return this._cache[A]},Mo.prototype.loadImage=function(A){return n(this,void 0,void 0,(function(){var e,t,n,o,i=this;return r(this,(function(r){switch(r.label){case 0:return e=So.isSameOrigin(A),t=!_o(A)&&!0===this._options.useCORS&&Pn.SUPPORT_CORS_IMAGES&&!e,n=!_o(A)&&!e&&!Go(A)&&"string"==typeof this._options.proxy&&Pn.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||_o(A)||Go(A)||n||t?(o=A,n?[4,this.proxy(o)]:[3,2]):[2];case 1:o=r.sent(),r.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var n=new Image;n.onload=function(){return A(n)},n.onerror=e,(ko(o)||t)&&(n.crossOrigin="anonymous"),n.src=o,!0===n.complete&&setTimeout((function(){return A(n)}),500),0<i._options.imageTimeout&&setTimeout((function(){return e("Timed out ("+i._options.imageTimeout+"ms) loading image")}),i._options.imageTimeout)}))];case 3:return[2,r.sent()]}}))}))},Mo.prototype.has=function(A){return void 0!==this._cache[A]},Mo.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},Mo.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise((function(r,o){var i=Pn.SUPPORT_RESPONSE_TYPE?"blob":"text",s=new XMLHttpRequest;s.onload=function(){var A;200===s.status?"text"==i?r(s.response):((A=new FileReader).addEventListener("load",(function(){return r(A.result)}),!1),A.addEventListener("error",(function(A){return o(A)}),!1),A.readAsDataURL(s.response)):o("Failed to proxy resource "+n+" with status code "+s.status)},s.onerror=o;var a,c=-1<t.indexOf("?")?"&":"?";s.open("GET",t+c+"url="+encodeURIComponent(A)+"&responseType="+i),"text"!=i&&s instanceof XMLHttpRequest&&(s.responseType=i),e._options.imageTimeout&&(a=e._options.imageTimeout,s.timeout=a,s.ontimeout=function(){return o("Timed out ("+a+"ms) proxying "+n)}),s.send()}))},Mo);function Mo(A,e){this.context=A,this._options=e,this._cache={}}var Do=/^data:image\/svg\+xml/i,jo=/^data:image\/.*;base64,/i,No=/^data:image\/.*/i,Ko=function(A){return Pn.SUPPORT_SVG_DRAWING||!Po(A)},_o=function(A){return No.test(A)},ko=function(A){return jo.test(A)},Go=function(A){return"blob"===A.substr(0,4)},Po=function(A){return"svg"===A.substr(-3).toLowerCase()||Do.test(A)},Ro=(Vo.prototype.add=function(A,e){return new Vo(this.x+A,this.y+e)},Vo);function Vo(A,e){this.type=0,this.x=A,this.y=e}function zo(A,e,t){return new Ro(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)}var Xo=(Jo.prototype.subdivide=function(A,e){var t=zo(this.start,this.startControl,A),n=zo(this.startControl,this.endControl,A),r=zo(this.endControl,this.end,A),o=zo(t,n,A);return n=zo(n,r,A),A=zo(o,n,A),e?new Jo(this.start,t,o,A):new Jo(A,n,r,this.end)},Jo.prototype.add=function(A,e){return new Jo(this.start.add(A,e),this.startControl.add(A,e),this.endControl.add(A,e),this.end.add(A,e))},Jo.prototype.reverse=function(){return new Jo(this.end,this.endControl,this.startControl,this.start)},Jo);function Jo(A,e,t,n){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=n}function Wo(A){return 1===A.type}var Yo,Zo=function(A){var e=A.styles,t=A.bounds,n=(p=re(e.borderTopLeftRadius,t.width,t.height))[0],r=p[1],o=(h=re(e.borderTopRightRadius,t.width,t.height))[0],i=h[1],s=(w=re(e.borderBottomRightRadius,t.width,t.height))[0],a=w[1],c=(C=re(e.borderBottomLeftRadius,t.width,t.height))[0],u=C[1];(Q=[]).push((n+o)/t.width),Q.push((c+s)/t.width),Q.push((r+u)/t.height),Q.push((i+a)/t.height),1<(m=Math.max.apply(Math,Q))&&(n/=m,r/=m,o/=m,i/=m,s/=m,a/=m,c/=m,u/=m);var l=t.width-o,d=t.height-a,B=t.width-s,g=t.height-u,f=e.borderTopWidth,p=e.borderRightWidth,h=e.borderBottomWidth,w=e.borderLeftWidth,C=ge(e.paddingTop,A.bounds.width),Q=ge(e.paddingRight,A.bounds.width),m=ge(e.paddingBottom,A.bounds.width);A=ge(e.paddingLeft,A.bounds.width),this.topLeftBorderDoubleOuterBox=0<n||0<r?ti(t.left+w/3,t.top+f/3,n-w/3,r-f/3,Yo.TOP_LEFT):new Ro(t.left+w/3,t.top+f/3),this.topRightBorderDoubleOuterBox=0<n||0<r?ti(t.left+l,t.top+f/3,o-p/3,i-f/3,Yo.TOP_RIGHT):new Ro(t.left+t.width-p/3,t.top+f/3),this.bottomRightBorderDoubleOuterBox=0<s||0<a?ti(t.left+B,t.top+d,s-p/3,a-h/3,Yo.BOTTOM_RIGHT):new Ro(t.left+t.width-p/3,t.top+t.height-h/3),this.bottomLeftBorderDoubleOuterBox=0<c||0<u?ti(t.left+w/3,t.top+g,c-w/3,u-h/3,Yo.BOTTOM_LEFT):new Ro(t.left+w/3,t.top+t.height-h/3),this.topLeftBorderDoubleInnerBox=0<n||0<r?ti(t.left+2*w/3,t.top+2*f/3,n-2*w/3,r-2*f/3,Yo.TOP_LEFT):new Ro(t.left+2*w/3,t.top+2*f/3),this.topRightBorderDoubleInnerBox=0<n||0<r?ti(t.left+l,t.top+2*f/3,o-2*p/3,i-2*f/3,Yo.TOP_RIGHT):new Ro(t.left+t.width-2*p/3,t.top+2*f/3),this.bottomRightBorderDoubleInnerBox=0<s||0<a?ti(t.left+B,t.top+d,s-2*p/3,a-2*h/3,Yo.BOTTOM_RIGHT):new Ro(t.left+t.width-2*p/3,t.top+t.height-2*h/3),this.bottomLeftBorderDoubleInnerBox=0<c||0<u?ti(t.left+2*w/3,t.top+g,c-2*w/3,u-2*h/3,Yo.BOTTOM_LEFT):new Ro(t.left+2*w/3,t.top+t.height-2*h/3),this.topLeftBorderStroke=0<n||0<r?ti(t.left+w/2,t.top+f/2,n-w/2,r-f/2,Yo.TOP_LEFT):new Ro(t.left+w/2,t.top+f/2),this.topRightBorderStroke=0<n||0<r?ti(t.left+l,t.top+f/2,o-p/2,i-f/2,Yo.TOP_RIGHT):new Ro(t.left+t.width-p/2,t.top+f/2),this.bottomRightBorderStroke=0<s||0<a?ti(t.left+B,t.top+d,s-p/2,a-h/2,Yo.BOTTOM_RIGHT):new Ro(t.left+t.width-p/2,t.top+t.height-h/2),this.bottomLeftBorderStroke=0<c||0<u?ti(t.left+w/2,t.top+g,c-w/2,u-h/2,Yo.BOTTOM_LEFT):new Ro(t.left+w/2,t.top+t.height-h/2),this.topLeftBorderBox=0<n||0<r?ti(t.left,t.top,n,r,Yo.TOP_LEFT):new Ro(t.left,t.top),this.topRightBorderBox=0<o||0<i?ti(t.left+l,t.top,o,i,Yo.TOP_RIGHT):new Ro(t.left+t.width,t.top),this.bottomRightBorderBox=0<s||0<a?ti(t.left+B,t.top+d,s,a,Yo.BOTTOM_RIGHT):new Ro(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=0<c||0<u?ti(t.left,t.top+g,c,u,Yo.BOTTOM_LEFT):new Ro(t.left,t.top+t.height),this.topLeftPaddingBox=0<n||0<r?ti(t.left+w,t.top+f,Math.max(0,n-w),Math.max(0,r-f),Yo.TOP_LEFT):new Ro(t.left+w,t.top+f),this.topRightPaddingBox=0<o||0<i?ti(t.left+Math.min(l,t.width-p),t.top+f,l>t.width+p?0:Math.max(0,o-p),Math.max(0,i-f),Yo.TOP_RIGHT):new Ro(t.left+t.width-p,t.top+f),this.bottomRightPaddingBox=0<s||0<a?ti(t.left+Math.min(B,t.width-w),t.top+Math.min(d,t.height-h),Math.max(0,s-p),Math.max(0,a-h),Yo.BOTTOM_RIGHT):new Ro(t.left+t.width-p,t.top+t.height-h),this.bottomLeftPaddingBox=0<c||0<u?ti(t.left+w,t.top+Math.min(g,t.height-h),Math.max(0,c-w),Math.max(0,u-h),Yo.BOTTOM_LEFT):new Ro(t.left+w,t.top+t.height-h),this.topLeftContentBox=0<n||0<r?ti(t.left+w+A,t.top+f+C,Math.max(0,n-(w+A)),Math.max(0,r-(f+C)),Yo.TOP_LEFT):new Ro(t.left+w+A,t.top+f+C),this.topRightContentBox=0<o||0<i?ti(t.left+Math.min(l,t.width+w+A),t.top+f+C,l>t.width+w+A?0:o-w+A,i-(f+C),Yo.TOP_RIGHT):new Ro(t.left+t.width-(p+Q),t.top+f+C),this.bottomRightContentBox=0<s||0<a?ti(t.left+Math.min(B,t.width-(w+A)),t.top+Math.min(d,t.height+f+C),Math.max(0,s-(p+Q)),a-(h+m),Yo.BOTTOM_RIGHT):new Ro(t.left+t.width-(p+Q),t.top+t.height-(h+m)),this.bottomLeftContentBox=0<c||0<u?ti(t.left+w+A,t.top+g,Math.max(0,c-(w+A)),u-(h+m),Yo.BOTTOM_LEFT):new Ro(t.left+w+A,t.top+t.height-(h+m))};function qo(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]}function $o(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]}function Ai(A){return 1===A.type}function ei(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))}(Ce=Yo=Yo||{})[Ce.TOP_LEFT=0]="TOP_LEFT",Ce[Ce.TOP_RIGHT=1]="TOP_RIGHT",Ce[Ce.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",Ce[Ce.BOTTOM_LEFT=3]="BOTTOM_LEFT";var ti=function(A,e,t,n,r){var o=(Math.sqrt(2)-1)/3*4,i=t*o,s=n*o,a=A+t,c=e+n;switch(r){case Yo.TOP_LEFT:return new Xo(new Ro(A,c),new Ro(A,c-s),new Ro(a-i,e),new Ro(a,e));case Yo.TOP_RIGHT:return new Xo(new Ro(A,e),new Ro(A+i,e),new Ro(a,c-s),new Ro(a,c));case Yo.BOTTOM_RIGHT:return new Xo(new Ro(a,e),new Ro(a,e+s),new Ro(A+i,c),new Ro(A,c));default:return Yo.BOTTOM_LEFT,new Xo(new Ro(a,c),new Ro(a-i,c),new Ro(A,e+s),new Ro(A,e))}},ni=function(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6},ri=function(A,e){this.path=A,this.target=e,this.type=1},oi=function(A){this.opacity=A,this.type=2,this.target=6},ii=function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]},si=(ai.prototype.getEffects=function(A){for(var e=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,n=this.effects.slice(0);t;){var r,o=t.effects.filter((function(A){return!Ai(A)}));e||0!==t.container.styles.position||!t.parent?(n.unshift.apply(n,o),e=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX&&(ei(qo(t.curves),r=$o(t.curves))||n.unshift(new ri(r,6)))):n.unshift.apply(n,o),t=t.parent}return n.filter((function(e){return Gt(e.target,A)}))},ai);function ai(A,e){var t,n;this.container=A,this.parent=e,this.effects=[],this.curves=new Zo(this.container),this.container.styles.opacity<1&&this.effects.push(new oi(this.container.styles.opacity)),null!==this.container.styles.transform&&(e=this.container.bounds.left+this.container.styles.transformOrigin[0].number,t=this.container.bounds.top+this.container.styles.transformOrigin[1].number,n=this.container.styles.transform,this.effects.push(new ni(e,t,n))),0!==this.container.styles.overflowX&&(ei(t=qo(this.curves),n=$o(this.curves))?this.effects.push(new ri(t,6)):(this.effects.push(new ri(t,2)),this.effects.push(new ri(n,4))))}function ci(A,e){switch(e){case 0:return wi(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return wi(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return wi(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return wi(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}}function ui(A){var e=A.bounds;return A=A.styles,e.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))}function li(A){var e=A.styles,t=A.bounds,n=ge(e.paddingLeft,t.width),r=ge(e.paddingRight,t.width),o=ge(e.paddingTop,t.width);return A=ge(e.paddingBottom,t.width),t.add(n+e.borderLeftWidth,o+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+n+r),-(e.borderTopWidth+e.borderBottomWidth+o+A))}function di(A,e,t){var n=(o=A,0===(r=Qi(A.styles.backgroundOrigin,e))?o.bounds:(2===r?li:ui)(o)),r=(s=A,0===(i=Qi(A.styles.backgroundClip,e))?s.bounds:(2===i?li:ui)(s)),o=Ci(Qi(A.styles.backgroundSize,e),t,n),i=o[0],s=o[1];return t=re(Qi(A.styles.backgroundPosition,e),n.width-i,n.height-s),[mi(Qi(A.styles.backgroundRepeat,e),t,o,n,r),Math.round(n.left+t[0]),Math.round(n.top+t[1]),i,s]}function Bi(A){return WA(A)&&A.value===Ie.AUTO}function gi(A){return"number"==typeof A}var fi=function(A,e,t,n){A.container.elements.forEach((function(r){var o=Gt(r.flags,4),i=Gt(r.flags,2),s=new si(r,A);Gt(r.styles.display,2048)&&n.push(s);var a,c,u,l,d=Gt(r.flags,8)?[]:n;o||i?(a=o||r.styles.isPositioned()?t:e,i=new ii(s),r.styles.isPositioned()||r.styles.opacity<1||r.styles.isTransformed()?(c=r.styles.zIndex.order)<0?(u=0,a.negativeZIndex.some((function(A,e){return c>A.element.container.styles.zIndex.order?(u=e,!1):0<u})),a.negativeZIndex.splice(u,0,i)):0<c?(l=0,a.positiveZIndex.some((function(A,e){return c>=A.element.container.styles.zIndex.order?(l=e+1,!1):0<l})),a.positiveZIndex.splice(l,0,i)):a.zeroOrAutoZIndexOrTransformedOrOpacity.push(i):(r.styles.isFloating()?a.nonPositionedFloats:a.nonPositionedInlineLevel).push(i),fi(s,i,o?i:t,d)):((r.styles.isInlineLevel()?e.inlineLevel:e.nonInlineLevel).push(s),fi(s,e,t,d)),Gt(r.flags,8)&&pi(r,d)}))},pi=function(A,e){for(var t=A instanceof lr?A.start:1,n=A instanceof lr&&A.reversed,r=0;r<e.length;r++){var o=e[r];o.container instanceof ar&&"number"==typeof o.container.value&&0!==o.container.value&&(t=o.container.value),o.listValue=go(t,o.container.styles.listStyleType,!0),t+=n?-1:1}},hi=function(A,e){var t=[];return Wo(A)?t.push(A.subdivide(.5,!1)):t.push(A),Wo(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},wi=function(A,e,t,n){var r=[];return Wo(A)?r.push(A.subdivide(.5,!1)):r.push(A),Wo(t)?r.push(t.subdivide(.5,!0)):r.push(t),Wo(n)?r.push(n.subdivide(.5,!0).reverse()):r.push(n),Wo(e)?r.push(e.subdivide(.5,!1).reverse()):r.push(e),r},Ci=function(A,e,t){var n=e[0],r=e[1],o=e[2],i=A[0],s=A[1];if(!i)return[0,0];if(te(i)&&s&&te(s))return[ge(i,t.width),ge(s,t.height)];var a=gi(o);if(WA(i)&&(i.value===Ie.CONTAIN||i.value===Ie.COVER))return gi(o)?t.width/t.height<o!=(i.value===Ie.COVER)?[t.width,t.width/o]:[t.height*o,t.height]:[t.width,t.height];var c=gi(n);if(e=gi(r),A=c||e,Bi(i)&&(!s||Bi(s)))return c&&e?[n,r]:a||A?A&&a?[c?n:r*o,e?r:n/o]:[c?n:t.width,e?r:t.height]:[t.width,t.height];if(a){var u=0,l=0;return te(i)?u=ge(i,t.width):te(s)&&(l=ge(s,t.height)),Bi(i)?u=l*o:s&&!Bi(s)||(l=u/o),[u,l]}if(u=null,l=null,te(i)?u=ge(i,t.width):s&&te(s)&&(l=ge(s,t.height)),null!==(u=null!==(l=null===u||s&&!Bi(s)?l:c&&e?u/n*r:t.height)&&Bi(i)?c&&e?l/r*n:t.width:u)&&null!==l)return[u,l];throw new Error("Unable to calculate background-size for element")},Qi=function(A,e){return void 0===(e=A[e])?A[0]:e},mi=function(A,e,t,n,r){var o=e[0],i=e[1],s=t[0],a=t[1];switch(A){case 2:return[new Ro(Math.round(n.left),Math.round(n.top+i)),new Ro(Math.round(n.left+n.width),Math.round(n.top+i)),new Ro(Math.round(n.left+n.width),Math.round(a+n.top+i)),new Ro(Math.round(n.left),Math.round(a+n.top+i))];case 3:return[new Ro(Math.round(n.left+o),Math.round(n.top)),new Ro(Math.round(n.left+o+s),Math.round(n.top)),new Ro(Math.round(n.left+o+s),Math.round(n.height+n.top)),new Ro(Math.round(n.left+o),Math.round(n.height+n.top))];case 1:return[new Ro(Math.round(n.left+o),Math.round(n.top+i)),new Ro(Math.round(n.left+o+s),Math.round(n.top+i)),new Ro(Math.round(n.left+o+s),Math.round(n.top+i+a)),new Ro(Math.round(n.left+o),Math.round(n.top+i+a))];default:return[new Ro(Math.round(r.left),Math.round(r.top)),new Ro(Math.round(r.left+r.width),Math.round(r.top)),new Ro(Math.round(r.left+r.width),Math.round(r.height+r.top)),new Ro(Math.round(r.left),Math.round(r.height+r.top))]}},Ui="Hidden Text",vi=(yi.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),n=this._document.createElement("img"),r=this._document.createElement("span"),o=this._document.body;return t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",o.appendChild(t),n.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",r.style.fontFamily=A,r.style.fontSize=e,r.style.margin="0",r.style.padding="0",r.appendChild(this._document.createTextNode(Ui)),t.appendChild(r),t.appendChild(n),e=n.offsetTop-r.offsetTop+2,t.removeChild(r),t.appendChild(this._document.createTextNode(Ui)),t.style.lineHeight="normal",n.style.verticalAlign="super",n=n.offsetTop-t.offsetTop+2,o.removeChild(t),{baseline:e,middle:n}},yi.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},yi);function yi(A){this._data={},this._document=A}Ce=function(A,e){this.context=A,this.options=e};var Fi,Ii=(e(bi,Fi=Ce),bi.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach((function(A){return e.applyEffect(A)}))},bi.prototype.applyEffect=function(A){this.ctx.save(),2===A.type&&(this.ctx.globalAlpha=A.opacity),0===A.type&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),Ai(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},bi.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},bi.prototype.renderStack=function(A){return n(this,void 0,void 0,(function(){return r(this,(function(e){switch(e.label){case 0:return A.element.container.styles.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},bi.prototype.renderNode=function(A){return n(this,void 0,void 0,(function(){return r(this,(function(e){switch(e.label){case 0:return Gt(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},bi.prototype.renderTextWithLetterSpacing=function(A,e,t){var n=this;0===e?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+t):zn(A.text).reduce((function(e,r){return n.ctx.fillText(r,e,A.bounds.top+t),e+n.ctx.measureText(r).width}),A.bounds.left)},bi.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=Ti(A.fontFamily).join(", "),n=XA(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,n,t].join(" "),t,n]},bi.prototype.renderTextNode=function(A,e){return n(this,void 0,void 0,(function(){var t,n,o,i,s,a,c=this;return r(this,(function(r){return o=this.createFontStyle(e),t=o[0],n=o[1],o=o[2],this.ctx.font=t,this.ctx.direction=1===e.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",o=this.fontMetrics.getMetrics(n,o),i=o.baseline,s=o.middle,a=e.paintOrder,A.textBounds.forEach((function(A){a.forEach((function(t){switch(t){case 0:c.ctx.fillStyle=ae(e.color),c.renderTextWithLetterSpacing(A,e.letterSpacing,i);var n=e.textShadow;n.length&&A.text.trim().length&&(n.slice(0).reverse().forEach((function(t){c.ctx.shadowColor=ae(t.color),c.ctx.shadowOffsetX=t.offsetX.number*c.options.scale,c.ctx.shadowOffsetY=t.offsetY.number*c.options.scale,c.ctx.shadowBlur=t.blur.number,c.renderTextWithLetterSpacing(A,e.letterSpacing,i)})),c.ctx.shadowColor="",c.ctx.shadowOffsetX=0,c.ctx.shadowOffsetY=0,c.ctx.shadowBlur=0),e.textDecorationLine.length&&(c.ctx.fillStyle=ae(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:c.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+i),A.bounds.width,1);break;case 2:c.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:c.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+s),A.bounds.width,1)}})));break;case 1:e.webkitTextStrokeWidth&&A.text.trim().length&&(c.ctx.strokeStyle=ae(e.webkitTextStrokeColor),c.ctx.lineWidth=e.webkitTextStrokeWidth,c.ctx.lineJoin=window.chrome?"miter":"round",c.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+i)),c.ctx.strokeStyle="",c.ctx.lineWidth=0,c.ctx.lineJoin="miter"}}))})),[2]}))}))},bi.prototype.renderReplacedElement=function(A,e,t){var n;t&&0<A.intrinsicWidth&&0<A.intrinsicHeight&&(n=li(A),e=$o(e),this.path(e),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,n.left,n.top,n.width,n.height),this.ctx.restore())},bi.prototype.renderNodeContent=function(A){return n(this,void 0,void 0,(function(){var e,t,n,o,i,a,c,u,l,d,B,g;return r(this,(function(r){switch(r.label){case 0:this.applyEffects(A.getEffects(4)),e=A.container,t=A.curves,n=e.styles,o=0,i=e.textNodes,r.label=1;case 1:return o<i.length?(a=i[o],[4,this.renderTextNode(a,n)]):[3,4];case 2:r.sent(),r.label=3;case 3:return o++,[3,1];case 4:if(!(e instanceof $n))return[3,8];r.label=5;case 5:return r.trys.push([5,7,,8]),[4,this.context.cache.match(e.src)];case 6:return l=r.sent(),this.renderReplacedElement(e,t,l),[3,8];case 7:return r.sent(),this.context.logger.error("Error loading image "+e.src),[3,8];case 8:if(e instanceof tr&&this.renderReplacedElement(e,t,e.canvas),!(e instanceof or))return[3,12];r.label=9;case 9:return r.trys.push([9,11,,12]),[4,this.context.cache.match(e.svg)];case 10:return l=r.sent(),this.renderReplacedElement(e,t,l),[3,12];case 11:return r.sent(),this.context.logger.error("Error loading svg "+e.svg.substring(0,255)),[3,12];case 12:return e instanceof Er&&e.tree?[4,new bi(this.context,{scale:this.options.scale,backgroundColor:e.backgroundColor,x:0,y:0,width:e.width,height:e.height}).render(e.tree)]:[3,14];case 13:a=r.sent(),e.width&&e.height&&this.ctx.drawImage(a,0,0,e.width,e.height,e.bounds.left,e.bounds.top,e.bounds.width,e.bounds.height),r.label=14;case 14:if(e instanceof Cr&&(u=Math.min(e.bounds.width,e.bounds.height),e.type===pr?e.checked&&(this.ctx.save(),this.path([new Ro(e.bounds.left+.39363*u,e.bounds.top+.79*u),new Ro(e.bounds.left+.16*u,e.bounds.top+.5549*u),new Ro(e.bounds.left+.27347*u,e.bounds.top+.44071*u),new Ro(e.bounds.left+.39694*u,e.bounds.top+.5649*u),new Ro(e.bounds.left+.72983*u,e.bounds.top+.23*u),new Ro(e.bounds.left+.84*u,e.bounds.top+.34085*u),new Ro(e.bounds.left+.39363*u,e.bounds.top+.79*u)]),this.ctx.fillStyle=ae(wr),this.ctx.fill(),this.ctx.restore()):e.type===hr&&e.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(e.bounds.left+u/2,e.bounds.top+u/2,u/4,0,2*Math.PI,!0),this.ctx.fillStyle=ae(wr),this.ctx.fill(),this.ctx.restore())),Li(e)&&e.value.length){switch(d=this.createFontStyle(n),B=d[0],u=d[1],d=this.fontMetrics.getMetrics(B,u).baseline,this.ctx.font=B,this.ctx.fillStyle=ae(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Hi(e.styles.textAlign),g=li(e),c=0,e.styles.textAlign){case 1:c+=g.width/2;break;case 2:c+=g.width}u=g.add(c,0,0,-g.height/2+1),this.ctx.save(),this.path([new Ro(g.left,g.top),new Ro(g.left+g.width,g.top),new Ro(g.left+g.width,g.top+g.height),new Ro(g.left,g.top+g.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Rn(e.value,u),n.letterSpacing,d),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Gt(e.styles.display,2048))return[3,20];if(null===e.styles.listStyleImage)return[3,19];if(0!==(d=e.styles.listStyleImage).type)return[3,18];l=void 0,d=d.url,r.label=15;case 15:return r.trys.push([15,17,,18]),[4,this.context.cache.match(d)];case 16:return l=r.sent(),this.ctx.drawImage(l,e.bounds.left-(l.width+10),e.bounds.top),[3,18];case 17:return r.sent(),this.context.logger.error("Error loading list-style-image "+d),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==e.styles.listStyleType&&(B=this.createFontStyle(n)[0],this.ctx.font=B,this.ctx.fillStyle=ae(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",g=new s(e.bounds.left,e.bounds.top+ge(e.styles.paddingTop,e.bounds.width),e.bounds.width,Ve(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Rn(A.listValue,g),n.letterSpacing,Ve(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),r.label=20;case 20:return[2]}}))}))},bi.prototype.renderStackContent=function(A){return n(this,void 0,void 0,(function(){var e,t,n,o,i,s,a,c,u,l,d,B,g,f,p;return r(this,(function(r){switch(r.label){case 0:return Gt(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:r.sent(),e=0,t=A.negativeZIndex,r.label=2;case 2:return e<t.length?(p=t[e],[4,this.renderStack(p)]):[3,5];case 3:r.sent(),r.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:r.sent(),n=0,o=A.nonInlineLevel,r.label=7;case 7:return n<o.length?(p=o[n],[4,this.renderNode(p)]):[3,10];case 8:r.sent(),r.label=9;case 9:return n++,[3,7];case 10:i=0,s=A.nonPositionedFloats,r.label=11;case 11:return i<s.length?(p=s[i],[4,this.renderStack(p)]):[3,14];case 12:r.sent(),r.label=13;case 13:return i++,[3,11];case 14:a=0,c=A.nonPositionedInlineLevel,r.label=15;case 15:return a<c.length?(p=c[a],[4,this.renderStack(p)]):[3,18];case 16:r.sent(),r.label=17;case 17:return a++,[3,15];case 18:u=0,l=A.inlineLevel,r.label=19;case 19:return u<l.length?(p=l[u],[4,this.renderNode(p)]):[3,22];case 20:r.sent(),r.label=21;case 21:return u++,[3,19];case 22:d=0,B=A.zeroOrAutoZIndexOrTransformedOrOpacity,r.label=23;case 23:return d<B.length?(p=B[d],[4,this.renderStack(p)]):[3,26];case 24:r.sent(),r.label=25;case 25:return d++,[3,23];case 26:g=0,f=A.positiveZIndex,r.label=27;case 27:return g<f.length?(p=f[g],[4,this.renderStack(p)]):[3,30];case 28:r.sent(),r.label=29;case 29:return g++,[3,27];case 30:return[2]}}))}))},bi.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},bi.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},bi.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var n=Wo(A)?A.start:A;0===t?e.ctx.moveTo(n.x,n.y):e.ctx.lineTo(n.x,n.y),Wo(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},bi.prototype.renderRepeat=function(A,e,t,n){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,n),this.ctx.fill(),this.ctx.translate(-t,-n)},bi.prototype.resizeImage=function(A,e,t){if(A.width===e&&A.height===t)return A;var n=(null!==(n=this.canvas.ownerDocument)&&void 0!==n?n:document).createElement("canvas");return n.width=Math.max(1,e),n.height=Math.max(1,t),n.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),n},bi.prototype.renderBackgroundImage=function(A){return n(this,void 0,void 0,(function(){var e,t,n,o,i,s;return r(this,(function(a){switch(a.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var o,i,s,a,c,u,l,d,B,g,f,p,h,w,C,Q,m;return r(this,(function(r){switch(r.label){case 0:if(0!==t.type)return[3,5];o=void 0,i=t.url,r.label=1;case 1:return r.trys.push([1,3,,4]),[4,n.context.cache.match(i)];case 2:return o=r.sent(),[3,4];case 3:return r.sent(),n.context.logger.error("Error loading background-image "+i),[3,4];case 4:return o&&(s=di(A,e,[o.width,o.height,o.width/o.height]),u=s[0],f=s[1],p=s[2],B=s[3],g=s[4],c=n.ctx.createPattern(n.resizeImage(o,B,g),"repeat"),n.renderRepeat(u,c,f,p)),[3,6];case 5:1===t.type?(m=di(A,e,[null,null,null]),u=m[0],f=m[1],p=m[2],B=m[3],g=m[4],C=function(A,e,t){var n="number"==typeof A?A:(i=e/2,n=(o=t)/2,i=ge((r=A)[0],e)-i,o=n-ge(r[1],o),(Math.atan2(o,i)+2*Math.PI)%(2*Math.PI)),r=Math.abs(e*Math.sin(n))+Math.abs(t*Math.cos(n)),o=e/2,i=t/2;return e=r/2,t=Math.sin(n-Math.PI/2)*e,[r,o-(e=Math.cos(n-Math.PI/2)*e),o+e,i-t,i+t]}(t.angle,B,g),w=C[0],s=C[1],l=C[2],Q=C[3],d=C[4],(m=document.createElement("canvas")).width=B,m.height=g,C=m.getContext("2d"),a=C.createLinearGradient(s,Q,l,d),ve(t.stops,w).forEach((function(A){return a.addColorStop(A.stop,ae(A.color))})),C.fillStyle=a,C.fillRect(0,0,B,g),0<B&&0<g&&(c=n.ctx.createPattern(m,"repeat"),n.renderRepeat(u,c,f,p))):2===t.type&&(Q=di(A,e,[null,null,null]),u=Q[0],l=Q[1],d=Q[2],B=Q[3],g=Q[4],w=0===t.position.length?[de]:t.position,f=ge(w[0],B),p=ge(w[w.length-1],g),C=function(A,e,t,n,r){var o,i,s=0,a=0;switch(A.size){case 0:0===A.shape?s=a=Math.min(Math.abs(e),Math.abs(e-n),Math.abs(t),Math.abs(t-r)):1===A.shape&&(s=Math.min(Math.abs(e),Math.abs(e-n)),a=Math.min(Math.abs(t),Math.abs(t-r)));break;case 2:0===A.shape?s=a=Math.min(ye(e,t),ye(e,t-r),ye(e-n,t),ye(e-n,t-r)):1===A.shape&&(a=(o=Math.min(Math.abs(t),Math.abs(t-r))/Math.min(Math.abs(e),Math.abs(e-n)))*(s=ye((i=Fe(n,r,e,t,!0))[0]-e,(i[1]-t)/o)));break;case 1:0===A.shape?s=a=Math.max(Math.abs(e),Math.abs(e-n),Math.abs(t),Math.abs(t-r)):1===A.shape&&(s=Math.max(Math.abs(e),Math.abs(e-n)),a=Math.max(Math.abs(t),Math.abs(t-r)));break;case 3:0===A.shape?s=a=Math.max(ye(e,t),ye(e,t-r),ye(e-n,t),ye(e-n,t-r)):1===A.shape&&(a=(o=Math.max(Math.abs(t),Math.abs(t-r))/Math.max(Math.abs(e),Math.abs(e-n)))*(s=ye((i=Fe(n,r,e,t,!1))[0]-e,(i[1]-t)/o)))}return Array.isArray(A.size)&&(s=ge(A.size[0],n),a=2===A.size.length?ge(A.size[1],r):s),[s,a]}(t,f,p,B,g),m=C[0],Q=C[1],0<m&&0<Q&&(h=n.ctx.createRadialGradient(l+f,d+p,0,l+f,d+p,m),ve(t.stops,2*m).forEach((function(A){return h.addColorStop(A.stop,ae(A.color))})),n.path(u),n.ctx.fillStyle=h,m!==Q?(w=A.bounds.left+.5*A.bounds.width,C=A.bounds.top+.5*A.bounds.height,m=1/(Q/=m),n.ctx.save(),n.ctx.translate(w,C),n.ctx.transform(1,0,0,Q,0,0),n.ctx.translate(-w,-C),n.ctx.fillRect(l,m*(d-C)+C,B,g*m),n.ctx.restore()):n.ctx.fill())),r.label=6;case 6:return e--,[2]}}))},n=this,o=0,i=A.styles.backgroundImage.slice(0).reverse(),a.label=1;case 1:return o<i.length?(s=i[o],[5,t(s)]):[3,4];case 2:a.sent(),a.label=3;case 3:return o++,[3,1];case 4:return[2]}}))}))},bi.prototype.renderSolidBorder=function(A,e,t){return n(this,void 0,void 0,(function(){return r(this,(function(n){return this.path(ci(t,e)),this.ctx.fillStyle=ae(A),this.ctx.fill(),[2]}))}))},bi.prototype.renderDoubleBorder=function(A,e,t,o){return n(this,void 0,void 0,(function(){var n;return r(this,(function(r){switch(r.label){case 0:return e<3?[4,this.renderSolidBorder(A,t,o)]:[3,2];case 1:return r.sent(),[2];case 2:return n=function(A,e){switch(e){case 0:return wi(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return wi(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return wi(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return wi(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(o,t),this.path(n),this.ctx.fillStyle=ae(A),this.ctx.fill(),n=function(A,e){switch(e){case 0:return wi(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return wi(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return wi(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return wi(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(o,t),this.path(n),this.ctx.fill(),[2]}}))}))},bi.prototype.renderNodeBackgroundAndBorders=function(A){return n(this,void 0,void 0,(function(){var e,t,n,o,i,s,a,c,u=this;return r(this,(function(r){switch(r.label){case 0:return this.applyEffects(A.getEffects(2)),e=A.container.styles,t=!se(e.backgroundColor)||e.backgroundImage.length,n=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],o=xi(Qi(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(o),this.ctx.clip(),se(e.backgroundColor)||(this.ctx.fillStyle=ae(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:r.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){u.ctx.save();var t,n,r,o,i=qo(A.curves),s=e.inset?0:1e4,a=(t=-s+(e.inset?1:-1)*e.spread.number,n=(e.inset?1:-1)*e.spread.number,r=e.spread.number*(e.inset?-2:2),o=e.spread.number*(e.inset?-2:2),i.map((function(A,e){switch(e){case 0:return A.add(t,n);case 1:return A.add(t+r,n);case 2:return A.add(t+r,n+o);case 3:return A.add(t,n+o)}return A})));e.inset?(u.path(i),u.ctx.clip(),u.mask(a)):(u.mask(i),u.ctx.clip(),u.path(a)),u.ctx.shadowOffsetX=e.offsetX.number+s,u.ctx.shadowOffsetY=e.offsetY.number,u.ctx.shadowColor=ae(e.color),u.ctx.shadowBlur=e.blur.number,u.ctx.fillStyle=e.inset?ae(e.color):"rgba(0,0,0,1)",u.ctx.fill(),u.ctx.restore()})),r.label=2;case 2:s=i=0,a=n,r.label=3;case 3:return s<a.length?0!==(c=a[s]).style&&!se(c.color)&&0<c.width?2!==c.style?[3,5]:[4,this.renderDashedDottedBorder(c.color,c.width,i,A.curves,2)]:[3,11]:[3,13];case 4:return r.sent(),[3,11];case 5:return 3!==c.style?[3,7]:[4,this.renderDashedDottedBorder(c.color,c.width,i,A.curves,3)];case 6:return r.sent(),[3,11];case 7:return 4!==c.style?[3,9]:[4,this.renderDoubleBorder(c.color,c.width,i,A.curves)];case 8:return r.sent(),[3,11];case 9:return[4,this.renderSolidBorder(c.color,i,A.curves)];case 10:r.sent(),r.label=11;case 11:i++,r.label=12;case 12:return s++,[3,3];case 13:return[2]}}))}))},bi.prototype.renderDashedDottedBorder=function(A,e,t,o,i){return n(this,void 0,void 0,(function(){var n,s,a,c,u,l,d,B,g,f,p;return r(this,(function(r){return this.ctx.save(),g=function(A,e){switch(e){case 0:return hi(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return hi(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return hi(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return hi(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(o,t),n=ci(o,t),2===i&&(this.path(n),this.ctx.clip()),l=Wo(n[0])?(s=n[0].start.x,n[0].start.y):(s=n[0].x,n[0].y),d=Wo(n[1])?(a=n[1].end.x,n[1].end.y):(a=n[1].x,n[1].y),c=0===t||2===t?Math.abs(s-a):Math.abs(l-d),this.ctx.beginPath(),3===i?this.formatPath(g):this.formatPath(n.slice(0,2)),u=e<3?3*e:2*e,l=e<3?2*e:e,3===i&&(l=u=e),d=!0,c<=2*u?d=!1:c<=2*u+l?(u*=B=c/(2*u+l),l*=B):(g=Math.floor((c+l)/(u+l)),B=(c-g*u)/(g-1),l=(g=(c-(g+1)*u)/g)<=0||Math.abs(l-B)<Math.abs(l-g)?B:g),d&&(3===i?this.ctx.setLineDash([0,u+l]):this.ctx.setLineDash([u,l])),3===i?(this.ctx.lineCap="round",this.ctx.lineWidth=e):this.ctx.lineWidth=2*e+1.1,this.ctx.strokeStyle=ae(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===i&&(Wo(n[0])&&(f=n[3],p=n[0],this.ctx.beginPath(),this.formatPath([new Ro(f.end.x,f.end.y),new Ro(p.start.x,p.start.y)]),this.ctx.stroke()),Wo(n[1])&&(f=n[1],p=n[2],this.ctx.beginPath(),this.formatPath([new Ro(f.end.x,f.end.y),new Ro(p.start.x,p.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},bi.prototype.render=function(A){return n(this,void 0,void 0,(function(){return r(this,(function(e){switch(e.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=ae(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),n=new si(A,null),r=new ii(n),fi(n,r,r,t=[]),pi(n.container,t),[4,this.renderStack(r)];case 1:return e.sent(),this.applyEffects([]),[2,this.canvas]}var t,n,r}))}))},bi);function bi(A,e){return(A=Fi.call(this,A,e)||this)._activeEffects=[],A.canvas=e.canvas||document.createElement("canvas"),A.ctx=A.canvas.getContext("2d"),e.canvas||(A.canvas.width=Math.floor(e.width*e.scale),A.canvas.height=Math.floor(e.height*e.scale),A.canvas.style.width=e.width+"px",A.canvas.style.height=e.height+"px"),A.fontMetrics=new vi(document),A.ctx.scale(A.options.scale,A.options.scale),A.ctx.translate(-e.x,-e.y),A.ctx.textBaseline="bottom",A._activeEffects=[],A.context.logger.debug("Canvas renderer initialized ("+e.width+"x"+e.height+") with scale "+e.scale),A}var Ei,Li=function(A){return A instanceof Fr||A instanceof Ur||A instanceof Cr&&A.type!==hr&&A.type!==pr},xi=function(A,e){switch(A){case 0:return qo(e);case 2:return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox];default:return $o(e)}},Hi=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},Si=["-apple-system","system-ui"],Ti=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===Si.indexOf(A)})):A},Oi=(e(Mi,Ei=Ce),Mi.prototype.render=function(A){return n(this,void 0,void 0,(function(){var e;return r(this,(function(t){switch(t.label){case 0:return e=kn(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,Di(e)];case 1:return e=t.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=ae(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(e,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},Mi);function Mi(A,e){return(A=Ei.call(this,A,e)||this).canvas=e.canvas||document.createElement("canvas"),A.ctx=A.canvas.getContext("2d"),A.options=e,A.canvas.width=Math.floor(e.width*e.scale),A.canvas.height=Math.floor(e.height*e.scale),A.canvas.style.width=e.width+"px",A.canvas.style.height=e.height+"px",A.ctx.scale(A.options.scale,A.options.scale),A.ctx.translate(-e.x,-e.y),A.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+e.width+"x"+e.height+" at "+e.x+","+e.y+") with scale "+e.scale),A}var Di=function(A){return new Promise((function(e,t){var n=new Image;n.onload=function(){e(n)},n.onerror=t,n.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},ji=(Ni.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,o([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Ni.prototype.getTime=function(){return Date.now()-this.start},Ni.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,o([this.id,this.getTime()+"ms"],A))},Ni.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn?console.warn.apply(console,o([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Ni.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,o([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Ni.instances={},Ni);function Ni(A){var e=A.id;A=A.enabled,this.id=e,this.enabled=A,this.start=Date.now()}var Ki=(_i.instanceCount=1,_i);function _i(A,e){this.windowBounds=e,this.instanceName="#"+_i.instanceCount++,this.logger=new ji({id:this.instanceName,enabled:A.logging}),this.cache=null!==(e=A.cache)&&void 0!==e?e:new Oo(this,A)}"undefined"!=typeof window&&So.setContext(window);var ki=function(A,e,t){var n=e.ownerDocument,r=n.documentElement?me(A,getComputedStyle(n.documentElement).backgroundColor):Le.TRANSPARENT,o=n.body?me(A,getComputedStyle(n.body).backgroundColor):Le.TRANSPARENT;return t="string"==typeof t?me(A,t):null===t?Le.TRANSPARENT:4294967295,e===n.documentElement?se(r)?se(o)?t:o:r:t};return function(A,e){return function(A,e){return n(void 0,void 0,void 0,(function(){var n,o,a,u,l,d,B,g,f,p,h,w,C,Q,m,U;return r(this,(function(r){switch(r.label){case 0:if(!A||"object"!=i(A))return[2,Promise.reject("Invalid element provided as first argument")];if(!(n=A.ownerDocument))throw new Error("Element is not attached to a Document");if(!(o=n.defaultView))throw new Error("Document is not attached to a Window");return C={allowTaint:null!==(Q=e.allowTaint)&&void 0!==Q&&Q,imageTimeout:null!==(p=e.imageTimeout)&&void 0!==p?p:15e3,proxy:e.proxy,useCORS:null!==(h=e.useCORS)&&void 0!==h&&h},Q=t({logging:null===(w=e.logging)||void 0===w||w,cache:e.cache},C),p={windowWidth:null!==(p=e.windowWidth)&&void 0!==p?p:o.innerWidth,windowHeight:null!==(h=e.windowHeight)&&void 0!==h?h:o.innerHeight,scrollX:null!==(w=e.scrollX)&&void 0!==w?w:o.pageXOffset,scrollY:null!==(C=e.scrollY)&&void 0!==C?C:o.pageYOffset},h=new s(p.scrollX,p.scrollY,p.windowWidth,p.windowHeight),w=new Ki(Q,h),p=null!==(C=e.foreignObjectRendering)&&void 0!==C&&C,C={allowTaint:null!==(Q=e.allowTaint)&&void 0!==Q&&Q,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:p,copyStyles:p},w.logger.debug("Starting document clone with size "+h.width+"x"+h.height+" scrolled to "+-h.left+","+-h.top),Q=new po(w,A,C),(C=Q.clonedReferenceElement)?[4,Q.toIFrame(n,h)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return a=r.sent(),m=Xr(C)||"HTML"===C.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");return A=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),t=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight)),new s(0,0,A,t)}(C.ownerDocument):c(w,C),u=m.width,l=m.height,d=m.left,B=m.top,g=ki(w,C,e.backgroundColor),m={canvas:e.canvas,backgroundColor:g,scale:null!==(m=null!==(m=e.scale)&&void 0!==m?m:o.devicePixelRatio)&&void 0!==m?m:1,x:(null!==(m=e.x)&&void 0!==m?m:0)+d,y:(null!==(m=e.y)&&void 0!==m?m:0)+B,width:null!==(m=e.width)&&void 0!==m?m:Math.ceil(u),height:null!==(m=e.height)&&void 0!==m?m:Math.ceil(l)},p?(w.logger.debug("Document cloned, using foreign object rendering"),[4,new Oi(w,m).render(C)]):[3,3];case 2:return f=r.sent(),[3,5];case 3:return w.logger.debug("Document cloned, element located at "+d+","+B+" with size "+u+"x"+l+" using computed rendering"),w.logger.debug("Starting DOM parsing"),U=Dr(w,C),g===U.styles.backgroundColor&&(U.styles.backgroundColor=Le.TRANSPARENT),w.logger.debug("Starting renderer for element at "+m.x+","+m.y+" with size "+m.width+"x"+m.height),[4,new Ii(w,m).render(U)];case 4:f=r.sent(),r.label=5;case 5:return null!==(U=e.removeContainer)&&void 0!==U&&!U||po.destroy(a)||w.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore"),w.logger.debug("Finished rendering"),[2,f]}}))}))}(A,e=void 0===e?{}:e)}},"object"==i(e)?A.exports=o():void 0===(r="function"==typeof(n=o)?n.call(e,t,e,A):n)||(A.exports=r)},"./src/sendBeaconReport.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return i}});var n=t("./src/util.js"),r=t("./src/log.js"),o=t("./src/config.js");const i=function(){window.addEventListener("beforeunload",(function(){r.LOG.report("page","bu",{tt:"离开页面"})}),!0),document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState?(r.LOG.report("page","hidden",{tt:n.UTIL.cutTooLongStr(document.title)}),function(A){var e=0,t=o.CONFIG.VISIT_INFO.browser;if(navigator.sendBeacon&&n.UTIL.needReport()&&A.length&&t){var i=r.LOG.getQueryStr(n.UTIL.getReportUrl()),s="".concat(o.CONFIG.GLOBAL_VARS.domain,"/fr?").concat(i);!function A(t){if(t.length&&!(e>=200)){var n=new Blob([JSON.stringify(t)],{type:"text/plain;charset=UTF-8"}),r=n.size/1024;if(r<64)navigator.sendBeacon(s,n),e++;else if(1!==t.length)for(var o=Math.ceil(r/64)+1,i=Math.ceil(t.length/o),a=0;a<o;a++){A(t.slice(a*i,(a+1)*i))}}}(A)}}(o.CONFIG.GLOBAL_VARS.logList),o.CONFIG.GLOBAL_VARS.logList=[]):"visible"===document.visibilityState&&r.LOG.report("page","visible",{tt:n.UTIL.cutTooLongStr(document.title)})}),!0)}},"./src/service.js":function(A,e,t){"use strict";t.r(e),t.d(e,{API:function(){return r}});var n=t("./src/util.js"),r={getEventList:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=n.UTIL.getAppId(),t=n.UTIL.getConfig(),r=t.searchDomain,o={appId:e,region:t.region,pageUrl:A};return new Promise((function(A,e){var t;null===(t=n.UTIL.httpRequest("".concat(r,"/nopoint/getNoPointEvent"),"post",o))||void 0===t||t.then((function(e){A(null==e?void 0:e.result)}))}))}}},"./src/style-design.js":function(A,e,t){"use strict";t.r(e),t.d(e,{getScrollNum:function(){return a},hasDoubleModal:function(){return s},hasDoubleShadow:function(){return i},hasMoreThanOneMainBtn:function(){return r},isUselessPager:function(){return o}});var n=t("./src/util.js");function r(A){for(var e=0,t=A.children,r=0;r<t.length;r++){var o,i=t[r];"d-button"===n.UTIL.getNodeName(i)&&null!=i&&null!==(o=i.children[0])&&void 0!==o&&null!==(o=o.className)&&void 0!==o&&o.includes("devui-btn-primary")&&(e+=1)}return e}function o(A){var e,t=null===(e=A.className)||void 0===e?void 0:e.toString().includes("devui-pagination-list"),n=A.children.length<=3;return t&&n}function i(A){if("none"!==getComputedStyle(A).boxShadow&&A.parentNode)for(var e=A.parentNode;e.nodeType===Node.ELEMENT_NODE;){if("none"!==getComputedStyle(e).boxShadow)return!0;e=e.parentNode}return!1}function s(A){var e=!1;return"d-modal"===n.UTIL.getNodeName(A)&&(e=document.getElementsByTagName("d-modal").length>1),e}function a(A){var e=["auto","scroll"],t=A.scrollWidth,n=A.clientWidth,r=A.scrollHeight,o=A.clientHeight,i=getComputedStyle(A)||{},s=i.overflowX,a=i.overflowY,c=e.includes(s)||e.includes(a),u=r>o&&c?1:0;return"".concat(t>n&&c?1:0," ").concat(u)}},"./src/style.js":function(A,e,t){"use strict";t.r(e),t.d(e,{getLinkType:function(){return d},getNodeStyles:function(){return u},spaceNotValid:function(){return B},uploadPageStyle:function(){return w},uploadVersion:function(){return l}});var n=t("./src/util.js"),r=t("./src/style-design.js"),o=t("./src/config.js"),i=0,s=1,a=function(A){var e=getComputedStyle(A),t=e.width,n=e.height,r="none"!==A.style.display&&"hidden"!==A.style.visibility&&!A.hidden&&"none"!==A.style.fill&&"none"!==A.style.stroke,o="auto"===t||parseInt(t),i="auto"===n||parseInt(n);return r&&o&&i},c=function(A,e){return!e.some((function(e){return e.contains(A)}))},u=function(A,e){var t=[],r=function(A,e){for(var t,n=[],r=[],o=document.createNodeIterator(document.body);t=o.nextNode();)t.childElementCount>=100&&r.push(t),c(t,r)&&p(t)&&!h(t,A,e)&&a(t)&&n.push(t);return n}(A,e).slice(0,1500);try{r.forEach((function(A){var e=g(A);e.fx&&t.push(e)}))}catch(A){n.UTIL.setFrVar({styleEnvErr:!0})}return t},l=function(A){if(n.UTIL.needReport()){var e=n.UTIL.getAppId(),t=o.CONFIG.GLOBAL_VARS.domain,r=document.getElementsByTagName("app-root")[0],i=document.querySelector("[ng-version]");if(r||i){var s={devuiVersion:(null==r?void 0:r.getAttribute("ng-devui-version"))||-1,devuiPlusVersion:(null==r?void 0:r.getAttribute("ng-devui-plus-version"))||-1,devuiCloudDragonVersion:(null==r?void 0:r.getAttribute("devui-clouddragon-version"))||-1,devuiIconVersion:(null==r?void 0:r.getAttribute("devui-assets-version"))||-1,consoleVersion:(null==r?void 0:r.getAttribute("devcloud-console-version"))||-1,angularVersion:(null==r?void 0:r.getAttribute("angular-version"))||-1,consoleAngularVersion:(null==i?void 0:i.getAttribute("ng-version"))||-1};n.UTIL.httpRequest("".concat(t,"/pkgVersion?appId=").concat(e,"&url=").concat(A),"post",s).catch((function(A){}))}}},d=function(A,e){var t=A.nodeName,n=A.className;if("A"!==t)return i;if(["d-accordion","d-pagination","d-breadcrumb","d-data-table","d-gantt","datepicker","d-editor"].some((function(A){return e.includes(A)})))return i;if(e.includes("d-tabs")){var r,o=A.parentNode||A.parentElement,a=null==o||null===(r=o.className)||void 0===r?void 0:r.toString();if(a.includes("devui-nav-tab-item")||a.includes("devui-tab-add-icon"))return i}return null!=n&&n.toString().includes("devui-link")?i:s},B=function(A,e){return A.split(" ").find((function(A){var t=parseFloat(A);return t%1||"auto"!==A&&t%e}))?1:0},g=function(A){var e=A.nodeName,t=A.className,o=A.id,a=A.textContent,c=getComputedStyle(A),u=c.color,l=c.fontSize,g=c.boxShadow,p=c.borderRadius,h=c.backgroundColor,w=c.borderColor,C=c.borderTop,Q=c.borderBottom,m=c.borderLeft,U=c.borderRight,v=c.margin,y=c.padding,F=c.top,I=c.right,b=c.bottom,E=c.left,L=c.textDecoration,x=c.lineHeight,H="".concat(F," ").concat(I," ").concat(b," ").concat(E),S=f(A),T=(0,r.getScrollNum)(A),O=n.UTIL.travelWithFunc(A,n.UTIL.fxFun);return{selector:n.UTIL.getCssSelector(A),fx:O,enm:e,eid:o,tc:a.replace(/\n/g,"/").slice(0,50),ecls:null==t?void 0:t.toString(),attributes:[{key:"color",value:u},{key:"fontSize",value:l},{key:"boxShadow",value:g},{key:"borderRadius",value:p},{key:"bgColor",value:h},{key:"borderColor",value:w},{key:"borderTop",value:C},{key:"borderBottom",value:Q},{key:"borderLeft",value:m},{key:"borderRight",value:U},{key:"margin",value:v,type:B(v,2)},{key:"padding",value:y,type:B(y,2)},{key:"absMargin",value:H,type:B(H,2)},{key:"td",value:L},{key:"lineHeight",value:x},{key:"iconSize",value:S,type:B(S,4)},{key:"uselessPager",value:(0,r.isUselessPager)(A)},{key:"mainBtnNum",value:(0,r.hasMoreThanOneMainBtn)(A)},{key:"doubleShadow",value:(0,r.hasDoubleShadow)(A)},{key:"doubleModal",value:(0,r.hasDoubleModal)(A)},{key:"scrollNum",value:T,type:T.includes("1")?s:i},{key:"linkStyle",value:null==t?void 0:t.toString(),type:d(A,O)},{key:"dp",value:n.UTIL.travelWithFunc(A,n.UTIL.dpFun)},{key:"propType",value:A.getAttribute("type")||""},{key:"childIndex",value:n.UTIL.calcNodeIndexOfParent(A)}]}},f=function(A){var e="",t=getComputedStyle(A),r=t.width,o=t.height,i=t.backgroundImage,s=parseInt(r)<=50&&parseInt(o)<=50,a=n.UTIL.getNodeName(A);return s&&("img"===a||"svg"===a||"none"!==i)&&(e="".concat(r," ").concat(o)),e},p=function(A){var e=1===A.nodeType,t="script"===A.nodeName.toLowerCase();return e&&!t},h=function(A,e,t){for(var n,r=A,o=function(){var A=r,n=A.id,o=A.className,i=e.find((function(A){return A===n})),s=t.find((function(A){return null==o?void 0:o.toString().split(" ").find((function(e){return e===A}))}));if(i||s)return{v:!0};r=r.parentNode};r.nodeType===Node.ELEMENT_NODE;)if(n=o())return n.v;return!1},w=function(A,e,t){if(n.UTIL.needReport()){var r=n.UTIL.getAppId(),i=o.CONFIG.GLOBAL_VARS,s=i.VERSION,a=i.domain,c=e?e.split(","):[],l=t?t.split(","):[],d=n.UTIL.getReportUrl(),B=o.CONFIG.VISIT_INFO,g=B.browser,f={os:B.os,brt:g.split(": ")[0],brv:g.split(": ")[1],sr:"".concat(window.screen.width,"*").concat(window.screen.height),ver:s,uid:n.UTIL.getUserId()};n.UTIL.httpRequest("".concat(a,"/v3/style?appId=").concat(r,"&url=").concat(encodeURIComponent(A),"&originUrl=").concat(d),"post",{clientInfo:JSON.stringify(f),styleList:u(c,l)}).catch((function(A){}))}},C=window.__fr||{};C.uploadPageStyle=w,C.uploadVersion=l},"./src/uba.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return c}});var n=t("./src/log.js"),r=t("./src/util.js"),o=t("./src/crash.js"),i=t("./src/fps.js"),s=t("./src/config.js");function a(){var A=function(A,e){var t,i=e.target,s={ecls:null===(t=i.className)||void 0===t?void 0:t.toString(),eid:i.id,enm:i.nodeName,px:parseInt(e.pageX),py:parseInt(e.pageY),tc:r.UTIL.getTextContent(i.textContent),tt:r.UTIL.cutTooLongStr(document.title),width:r.UTIL.getPageWidth(),height:r.UTIL.getPageHeight(),xpath:r.UTIL.getCssSelector(i),fx:r.UTIL.travelWithFunc(i,r.UTIL.fxFun),dp:r.UTIL.travelWithFunc(i,r.UTIL.dpFun),cp:r.UTIL.travelWithFunc(i,r.UTIL.cpFun)};n.LOG.report("click",A,s),function(A){if(!window.Worker||!window.furionWorker)return;var e=A.ecls,t=A.enm,n=A.fx,r=A.tc,i=A.xpath;window.furionWorker.postMessage({checkTime:(new Date).getTime(),jsHeap:o.CRASH.getJSHeap(),type:"reply",lastClickInfo:{ecls:e,enm:t,fx:n,tc:r,xpath:i,ts:(new Date).getTime()}})}(s)};window.addEventListener("click",(function(e){(function(A){var e;if(!A.target||!A.target.parentElement||null!==(e=document.getElementById("furion-point"))&&void 0!==e&&null!==(e=e.classList)&&void 0!==e&&e.contains("navbar-tab-item-active")||A.target.getAttribute("fr_atr"))return!1;var t=[A.target.className,A.target.parentElement.className].join(" "),n=A.target.nodeName;return t||"A"===n})(e)&&A("click",e)}),!0),window.addEventListener("dbclick",(function(e){A("dbclick",e)}),!0),window.addEventListener("contextmenu",(function(e){A("contextmenu",e)}),!0)}const c=function(){!function(){var A=function(A){var e=history[A];return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=e.apply(this,n),i=document.createEvent("HTMLEvents");return i.initEvent(A,!1,!0),i.arguments=n,window.dispatchEvent(i),o}};history.pushState=A("pushState"),history.replaceState=A("replaceState"),window.addEventListener("pushState",(function(A){e("ps"),s.CONFIG.FPS_REQUEST_ID&&(window.cancelAnimationFrame(s.CONFIG.FPS_REQUEST_ID),(0,i.default)())}),!0),window.addEventListener("replaceState",(function(A){e("rs"),s.CONFIG.FPS_REQUEST_ID&&(window.cancelAnimationFrame(s.CONFIG.FPS_REQUEST_ID),(0,i.default)())}));var e=function(A){var e=r.UTIL.getConfig().hashMode;window.location.href.includes("#")&&!e||n.LOG.handlerPvReport(A)}}(),window.addEventListener("hashchange",(function(A){n.LOG.handlerPvReport("hs")})),r.UTIL.getConfig().closeClickInfo||a()}},"./src/util.js":function(A,e,t){"use strict";t.r(e),t.d(e,{UTIL:function(){return k},limitLen:function(){return h}});var n=t("./node_modules/uuid/dist/esm-browser/v4.js"),r=t("./node_modules/flatted/esm/index.js"),o=t("./src/config.js"),i=t("./src/fps.js");function s(A,e){return function(A){if(Array.isArray(A))return A}(A)||function(A,e){var t=null==A?null:"undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(null!=t){var n,r,o,i,s=[],a=!0,c=!1;try{if(o=(t=t.call(A)).next,0===e){if(Object(t)!==t)return;a=!1}else for(;!(a=(n=o.call(t)).done)&&(s.push(n.value),s.length!==e);a=!0);}catch(A){c=!0,r=A}finally{try{if(!a&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(c)throw r}}return s}}(A,e)||u(A,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(A){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(A){return typeof A}:function(A){return A&&"function"==typeof Symbol&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},a(A)}function c(A){return function(A){if(Array.isArray(A))return l(A)}(A)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(A)||u(A)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(A,e){if(A){if("string"==typeof A)return l(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?l(A,e):void 0}}function l(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}function d(A,e){var t=Object.keys(A);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(A);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(A,e).enumerable}))),t.push.apply(t,n)}return t}function B(A){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?d(Object(t),!0).forEach((function(e){g(A,e,t[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(A,Object.getOwnPropertyDescriptors(t)):d(Object(t)).forEach((function(e){Object.defineProperty(A,e,Object.getOwnPropertyDescriptor(t,e))}))}return A}function g(A,e,t){return(e=function(A){var e=function(A,e){if("object"!=a(A)||!A)return A;var t=A[Symbol.toPrimitive];if(void 0!==t){var n=t.call(A,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(A)}(A,"string");return"symbol"==a(e)?e:e+""}(e))in A?Object.defineProperty(A,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):A[e]=t,A}var f=["#furion-point","#furion-browsing","#furion-heat-map","#furion-consistency"],p=["cn-southwest-242-console.huaweicloud.com","cn-north-5-console.huaweicloud.com","la-south-2-console.huaweicloud.com","sa-brazil-1-console.huaweicloud.com","la-north-2-console.huaweicloud.com","af-south-1-console.huaweicloud.com","tr-west-1-console.huaweicloud.com","me-east-1-console.huaweicloud.com"],h=3072,w=function(A,e,t){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"/";if(k.isValidAnalysisCookie()||!(t>0)){var i=new Date;if(i.setTime(i.getTime()+60*t*1e3),o.CONFIG.GLOBAL_VARS.isAvailableCookie){var s="".concat(A,"=").concat(encodeURIComponent(e),";expires=").concat(i.toGMTString(),";path=").concat(r);if(n){var a=document.domain.split("."),c=[];for(c.unshift(a.pop());a.length;){c.unshift(a.pop());var u=c.join(".");if(document.cookie="".concat(s,";domain=").concat(u),null!==C(A))return}}document.cookie=s}else localStorage.setItem(A,JSON.stringify({expires:i.getTime(),value:e}))}};function C(A){if(!o.CONFIG.GLOBAL_VARS.isAvailableCookie){var e,t=(new Date).getTime(),n=JSON.parse(localStorage.getItem(A)||null);return t>((null==n?void 0:n.expires)||Number.MIN_SAFE_INTEGER)?null:null===(e=JSON.parse(localStorage.getItem(A)))||void 0===e?void 0:e.value}var r,i=new RegExp("(^| )"+A+"=([^;]*)(;|$)");return(r=document.cookie.match(i))?decodeURIComponent(r[2]):null}function Q(A){var e=!1,t=navigator.mimeTypes;for(var n in t)if("application/360softmgrplugin"===t[n].type){e=!0;break}if(!e){var r=navigator.plugins;for(var o in r)if("internal-nacl-plugin"===r[o].filename){e=!0;break}}if(e){if(A.indexOf("chrome")>-1)return A.match(/chrome\/([\d\.]+)/);if(A.indexOf("msie")>-1)return A.match(/msie ([\d\.]+)/)}return null}function m(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=F(),t=e.appId,n=e.customAppId,r="";if(window.getConsoleContext){var o,i=null===(o=window.getConsoleContext())||void 0===o?void 0:o.get({name:"config"});r=null==i?void 0:i.get("endpointId")}var s=t||r;return n&&(s=n(s,A||k.getReportUrl())),s}function U(){var A,e=(null===(A=window.__fr.config)||void 0===A?void 0:A.uid)||window.__fr.extend,t=C(o.CONFIG.COOKIES.w3Fid)||C("currentUser"),n=K();return(e||t||(null==n?void 0:n.userId)||"").replace(o.CONFIG.SPECIAL_CHARS,"")}var v=function(A){var e;return null===(e=A.nodeName)||void 0===e?void 0:e.toString().toLowerCase()};function y(A,e){A&&e&&(o.CONFIG.GLOBAL_VARS.IAMInfo={userId:A,domainId:e})}var F=function(){var A=window.__fr||{},e=A.config||{},t=e.region,n=e.domain,r=e.searchDomain,o=e.frUrl,i=e.setting,s=e.closeReportFMP,a=void 0===s||s,u=e.filterApi,l=void 0===u?[]:u,d=e.smartJsErr,g=A.filterApi||[];t=t||"cn-north-1",i=i?"perf,".concat(i):"perf,uba,jsTrack,api,longtask",d=!1!==d;var f="https://furion.cloudbu.huawei.com/furion/home/<USER>/multi_dimensional";return n=n||"https://furiondata.myhuaweicloud.com/furiondataserver",r=r||"https://furion.cloudbu.huawei.com/furion/openapi",o=o||"https://furion.cloudbu.huawei.com/furion/home/<USER>/events",f="https://furion.cloudbu.huawei.com/furion/home/<USER>/multi_dimensional",B(B({},A.config),{},{region:t,domain:n,searchDomain:r,frUrl:o,setting:i,closeReportFMP:a,filterApi:["furiondataserver"].concat(c(l),c(g)),smartJsErr:d,multFrUrl:f,yMultFrUrl:"https://furion-y.cloudbu.huawei.com/furion/home/<USER>/multi_dimensional"})},I=function(A){Object.assign(window.__fr.config,A),null!=A&&A.uid&&w(o.CONFIG.COOKIES.w3Fid,A.uid,525600),null!=A&&A.IAMUserId&&null!=A&&A.IAMDomainId&&y(A.IAMUserId,A.IAMDomainId)},b=function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],t=F(),n=t.host,r=t.hashMode,o=A||window.location,i=o.protocol,s=o.pathname,a=o.search,c=o.hash,u=o.href,l=n?"".concat(i,"//").concat(n).concat(s).concat(r?c:a):u;return E(l,e)},E=function(A,e){var t="";if(F().hashMode)if(e)t=A;else{var n,r=A.split("?")[0],o=null===(n=A.split("?")[1])||void 0===n?void 0:n.split("#")[1];t=o?"".concat(r,"#").concat(o):r}else{var i=A.split("?")[1],s=A.split("?")[0].split("#")[0],a=i?"?".concat(i):"";t=e?"".concat(s).concat(a):s}return encodeURIComponent(t)},L=function(A){var e=A||window.location.host;if(p.includes(e))return!1;return/.*-console.(eu.)?huaweicloud.com/.test(e)},x=function(){var A=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)||window.location,e=A.host,t=A.href;return"console-intl.huaweicloud.com"===e||0===t.indexOf("https://www.huaweicloud.com/intl")},H=function(){if(!x(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null))return!0;var A=C("hwcp_intl");if(!A)return!0;try{var e=JSON.parse(decodeURI(A));return!e||e.analysis}catch(A){return!0}},S=function(){var A=(0,n.default)().split("-").join("");o.CONFIG.VISIT_INFO.pvid=A,o.CONFIG.GLOBAL_VARS.lastPvid=A},T=function(){var A=(0,n.default)().split("-").join("");w(o.CONFIG.COOKIES.ssid,A,30,!0),o.CONFIG.GLOBAL_VARS.lastSsid=A},O=function(A){return!F().filterApi.find((function(e){return A.includes(e)}))},M=function(A){var e=F().crossOriginApi,t=void 0!==e&&e,n=!["hwa.his.huawei.com","arms","tingyun.com","furion"].find((function(e){return A.includes(e)}));return t&&O(A)&&n};function D(A){return A.replace(/["]/g,(function(A){return"\\".concat(A)}))}function j(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/\n/g,"/").replace(/\t/g," ").slice(0,50)}function N(){if(window.getConsoleContext)try{window.getConsoleContext().get({name:"dataService"}).getUserData().then((function(A){null!=A&&A.userId&&null!=A&&A.domainId&&y(A.userId,A.domainId)}))}catch(A){}}function K(){return o.CONFIG.GLOBAL_VARS.IAMInfo||function(){try{var A,e,t,n;if(null!==(A=window.top._ubaParams)&&void 0!==A&&A.IAMUserID&&null!==(e=window.top._ubaParams)&&void 0!==e&&e.userAccount)return y(null===(t=window.top._ubaParams)||void 0===t?void 0:t.IAMUserID,null===(n=window.top._ubaParams)||void 0===n?void 0:n.userAccount),!0}catch(A){}return!1}()||N(),o.CONFIG.GLOBAL_VARS.IAMInfo}function _(A){if("function"==typeof window.URLSearchParams&&A){var e,t=(null===(e=A.split("?"))||void 0===e?void 0:e[1])||"",n=new URLSearchParams(t);if(n.size>0)return Array.from(n.entries()).reduce((function(A,e){var t=s(e,2),n=t[0],r=t[1];return A[n]=r,A}),{})}return null}var k={getBrowserInfo:function(){var A={name:"Others",version:"Unknown"},e=navigator.userAgent.toLowerCase(),t=function(A){return A.indexOf("metasr")>-1||A.indexOf("se 2.x")>-1}(e),n={IE:e.match(/rv:([\d.]+)\) like gecko/)||e.match(/msie ([\d\.]+)/),Chrome:e.match(/chrome\/([\d\.]+)/),Edge:e.match(/(?:edg|edge)\/([\d\.]+)/),Safari:e.match(/version\/([\d\.]+).*safari/),Firefox:e.match(/firefox\/([\d\.]+)/),Opera:e.match(/(?:opera|opr).([\d\.]+)/),Sogou:t?e.match(/msie ([\d\.]+)/):null,360:Q(e),QQBrowser:e.match(/qqbrowser\/([\d\.]+)/),MQQBrowser:e.match(/mqqbrowser\/([\d\.]+)/),MicroMessenger:e.match(/micromessenger\/([\d\.]+)/),UCBrowser:e.match(/(?:ucbrowser|ubrowser)\/([\d\.]+)/),HuaweiBrowser:e.match(/huaweibrowser\/([\d\.]+)/),BaiduBoxApp:e.match(/baiduboxapp\/([\d\.]+)/)};for(var r in n)n[r]&&(A.name=r,A.version=n[r][1]||"Unknown");return"".concat(A.name,": ").concat(A.version)},getOperationSys:function(){var A=navigator,e=A.platform,t=A.userAgent,n=t.toLowerCase(),r={Windows:["Win32","Windows"].includes(e),Mac:["Mac68K","MacPPC","Macintosh","MacIntel"].includes(e),iphone:n.includes("iphone"),ipad:n.includes("ipad"),Android:n.includes("android")};return Object.keys(r).find((function(A){return r[A]}))||e||t},getCssSelector:function(A){if(!(A instanceof Element||A instanceof HTMLElement))return"";for(var e=[];(null===(t=A)||void 0===t?void 0:t.nodeType)===Node.ELEMENT_NODE;){var t,n=v(A);if(A.id){n+="#".concat(A.id),e.unshift(n);break}for(var r=A,o=1;r=r.previousElementSibling;)v(r)===n&&o++;1!==o&&(n+=":nth-of-type(".concat(o,")")),e.unshift(n),A=A.parentNode}return e.join(" > ")},getPageWidth:function(){var A=document;return Math.max(Math.max(A.body.scrollWidth,A.documentElement.scrollWidth),Math.max(A.body.offsetWidth,A.documentElement.offsetWidth),Math.max(A.body.clientWidth,A.documentElement.clientWidth))},getPageHeight:function(){var A=document;return Math.max(Math.max(A.body.scrollHeight,A.documentElement.scrollHeight),Math.max(A.body.offsetHeight,A.documentElement.offsetHeight),Math.max(A.body.clientHeight,A.documentElement.clientHeight))},safeStringify:function(A){if("undefined"==typeof Set)return JSON.stringify(A);var e=new Set;return JSON.stringify(A,(function(A,t){if("object"===a(t)&&null!==t){if(e.has(t))return null;e.add(t)}return t}))},getCookie:C,setCookie:w,getUserId:U,getAppId:m,getConfig:F,setConfig:I,setFrVar:function(A){Object.assign(window.__fr,A)},setUid:function(A){if(A){var e=U();e&&e!==A&&(T(),S()),window.__fr.config&&(window.__fr.config.uid=A,w(o.CONFIG.COOKIES.w3Fid,A,525600))}},clearBufferOnfull:function(){performance.onresourcetimingbufferfull=performance.onwebkitresourcetimingbufferfull=function(){var A="clearResourceTimings";performance[A]||(A="webkitClearResourceTimings"),performance[A]()}},getIsInIframe:function(){try{return window.self!==window.top}catch(A){return!0}},convertUrl:E,getReportUrl:b,isFiltedUrl:function(A){var e=window.__fr.filterUrl||[],t=F().filterUrl||[];return e.concat(t).map((function(A){return encodeURIComponent(A)})).find((function(e){return A.includes(e)}))},checkHashMode:function(){location.href.split("?")[0].indexOf("#/")>-1&&void 0===F().hashMode&&I({hashMode:!0})},extendSsid:function(){var A=C(o.CONFIG.COOKIES.ssid);A&&(w(o.CONFIG.COOKIES.ssid,A,30,!0),o.CONFIG.GLOBAL_VARS.lastSsid=A)},resetPvid:S,resetSsid:T,oneDecimalDivide:function(A,e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return e?Math.round(A/e*t*10)/10:0},getNodeName:v,travelWithFunc:function(A,e){if(!(A instanceof Element||A instanceof HTMLElement))return"";for(var t=[];(null===(n=A)||void 0===n?void 0:n.nodeType)===Node.ELEMENT_NODE;){var n;t.unshift(e(A)),A=A.parentNode}return"/".concat(t.join("/"))},fxFun:function(A){for(var e=v(A),t=A,n=1;t=t.previousElementSibling;)v(t)===e&&n++;return(function(A){var e=A.parentNode||A.parentElement;return[].slice.call((null==e?void 0:e.children)||[]).filter((function(e){return v(e)===v(A)})).length>=2}(A)||n>1)&&(e+="[".concat(n,"]")),e},dpFun:function(A){return["ddropdown","dtextinput","dtextarea","dstepsguide","dloading","dpopover","dtooltip","dautocompelete","ddraggable","ddroppable","danchorbox","dmention","dreadtip","dChartCardSubtitle","dChartCardTitle"].find((function(e){return null!==A.getAttribute(e)}))||"-"},cpFun:function(A){var e,t=null===(e=A.className)||void 0===e?void 0:e.toString().split(" ");return o.CONFIG.GLOBAL_VARS.cp.find((function(A){return t.includes(A)}))||"-"},apiCanReport:function(A){var e=A.split(/[?|#]/)[0];return!e.includes("//")||e.includes(window.location.host)?O(e):M(e)},getTextContent:function(){var A=D(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"");try{return encodeURIComponent(j(A))}catch(e){return j(A)}},cutTooLongStr:j,getPositiveInt:function(A){var e=Math.round(A);return e>0?e:0},turnToString:function(A){var e;return null===(e="object"===a(A)?D((0,r.stringify)(A)):null==A?void 0:A.replace(/"/g,""))||void 0===e?void 0:e.slice(0,o.CONFIG.MAX_LEN.JS_ERR_VAL)},escapeStr:D,httpRequest:function(A,e,t){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:3e3;return o.CONFIG.GLOBAL_VARS.isWeCodeApp?function(A,e,t){return new Promise((function(n,r){var o={method:e,headers:{"Content-Type":"application/json"}};"post"===e&&(o=B(B({},o),{},{body:JSON.stringify(t)})),window.HWH5.fetchInternet(A,o).then((function(A){A.json().then((function(A){n(A)}))})).catch((function(A){r(A)}))}))}(A,e,t):function(A,e,t,n){return new Promise((function(r,o){var i=new XMLHttpRequest;i.timeout=n,i.open(e,A),i.send(JSON.stringify(t)),i.onreadystatechange=function(){if(4===i.readyState)try{var A=JSON.parse(i.responseText);A.result&&r(A)}catch(A){o(A)}}}))}(A,e,t,n)},calcNodeIndexOfParent:function(A){for(var e=A||{},t=-1;e;)t++,e=e.previousElementSibling;return t},getQueryVariable:function(A){for(var e=window.location.search.substring(1).split("&"),t=0;t<e.length;t++){var n=e[t].split("=");if(n[0]===A)return n[1]}return!1},addLoadEvent:function(A){window.addEventListener("load",(function(){A()}),!0)},isStationBlacklist:L,needReport:function(){var A,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=e||{},n=t.host,r=void 0===n?"":n,o=t.href,i=m(void 0===o?"":o);return!(!i||i.length>50||(A=void 0!==navigator.userAgent?navigator.userAgent.toLowerCase():"",/baiduspider|googlebot|bingbot|bytespider|yandexbot|ahrefsbot|cincraw|hotwhale/i.test(A))||L(r)||!H(e)||function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=window.__fr.filterPage||[],t=F().filterPage||[],n=e.concat(t).map((function(A){return encodeURIComponent(A)})),r=b(A);return n.find((function(A){return r.includes(A)}))}(e))},isValidAnalysisCookie:H,isIntlStation:x,getEncodedUserInfo:function(){var A=K();return A?encodeURIComponent(JSON.stringify(A)):null},getABtestParams:function(){if(window.ab_bucket)return encodeURIComponent(JSON.stringify(window.ab_bucket));var A=o.CONFIG.GLOBAL_VARS.ABtestInfo;return A?encodeURIComponent(JSON.stringify([A])):null},getApiPathWithQuery:function(A){var e,t=F().options,n=void 0===t?{}:t,r=A;return r.split(/[?|#]/)[0].includes(window.location.host)&&(r=A.replace(/^(http|https):\/\/[^/]+/,"")),null!==(e=n.api)&&void 0!==e&&e.hideQuery?r.split(/[?|#]/)[0]:r},getReportParams:function(A,e){var t=(k.getConfig().options||{}).api,n=void 0===t?{}:t,r={};return!1!==n.body&&e&&(r.body=e),!1!==n.query&&_(A)&&(r.query=_(A)),JSON.stringify(r).substring(0,2*h)},getReportHeaders:function(A){var e=(k.getConfig().options||{}).api,t=((void 0===e?{}:e).headers||[]).concat(["X-Passthrough-Error","cf2-x-hw-request-trace-id","x-request-id","furion_traceid","lubanops-ntrace-id","wise_traceid"]),n=c(new Set(t)),r={};return n.forEach((function(e){var t,n=A.get(((t=e).charAt(0).toLowerCase()+t.slice(1)).replace(/-([a-zA-Z])/g,(function(A,e){return"-".concat(e.toLowerCase())})));n&&(r[e]=n)})),JSON.stringify(r).substring(0,h)},setIAMInfoFromCFUI:N,handlePageVisibilityChange:function(){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&o.CONFIG.FPS_REQUEST_ID&&(window.cancelAnimationFrame(o.CONFIG.FPS_REQUEST_ID),(0,i.default)())}),!0)},getEleOfSelector:function(A){var e=null;try{e=document.querySelector(A)}catch(A){}return e},getEleOfXpath:function(A){var e=null;try{e=document.evaluate(A,document,null,XPathResult.ANY_TYPE,null).iterateNext()}catch(A){}return e},twoDecimalDivide:function(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;return e?Math.round(A/e*t*100)/100:0},forbidClick:function(){var A=document.querySelector(".code-less-box").shadowRoot;f.forEach((function(e){A.querySelector(e).classList.add("unclickable")}))},recoverClick:function(){var A=document.querySelector(".code-less-box").shadowRoot;f.forEach((function(e){A.querySelector(e).classList.remove("unclickable")}))},hasPermissions:function(){var A=document.querySelector('meta[http-equiv="content-security-policy"]');if(!A)return!0;var e=A.getAttribute("content");return!e||(!e.includes("default-src")&&!e.includes("connect-src")||(e.includes("*.cn")||e.includes("*.myhuaweicloud.cn")))},checkSdkWcm:function(){return c(document.getElementsByTagName("script")).some((function(A){return A.src.includes("furion-cdn.min.js")&&A.src.includes("wsr.his-op.huawei.com")}))},responseHasErr:function(A,e){var t=k.getConfig().parseApiErr;if(t)return t(e);var n=["failed","error"].includes(e.status),r=!1===e.success,o=e.error_msg||!1,i=!!e.statusCode&&parseInt(e.statusCode)>=400;return A>=400||n||r||o||i},removeTabActiveClass:function(){var A=document.querySelector(".code-less-box").shadowRoot;f.forEach((function(e){A.querySelector(e).classList.remove("navbar-tab-item-active")}))},getDeviceIsMobile:function(){var A=navigator.userAgent||navigator.vendor||window.opera;return/android|iphone|ipad|ipod|blackberry|openHarmony|windows phone/i.test(A.toLowerCase())},getFullApiPath:function(A){if(A.startsWith("http"))return A;try{return new URL(A,window.location.origin).href}catch(A){return""}},validateWorker:function(){if(!window.Worker)return!1;try{var A,e=(null===(A=document.head.querySelector('meta[http-equiv="Content-Security-Policy"]'))||void 0===A?void 0:A.getAttribute("content"))||"";if(e){var t=e.split(";").filter((function(A){return A.startsWith("worker-src")}))[0];if(t&&!t.split(" ").includes("blob:"))return!1}return!0}catch(A){return!1}}}},"./src/vital.js":function(A,e,t){"use strict";function n(A,e){var t="undefined"!=typeof Symbol&&A[Symbol.iterator]||A["@@iterator"];if(!t){if(Array.isArray(A)||(t=o(A))||e&&A&&"number"==typeof A.length){t&&(A=t);var n=0,r=function(){};return{s:r,n:function(){return n>=A.length?{done:!0}:{done:!1,value:A[n++]}},e:function(A){throw A},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,a=!1;return{s:function(){t=t.call(A)},n:function(){var A=t.next();return s=A.done,A},e:function(A){a=!0,i=A},f:function(){try{s||null==t.return||t.return()}finally{if(a)throw i}}}}function r(A){return function(A){if(Array.isArray(A))return i(A)}(A)||function(A){if("undefined"!=typeof Symbol&&null!=A[Symbol.iterator]||null!=A["@@iterator"])return Array.from(A)}(A)||o(A)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(A,e){if(A){if("string"==typeof A)return i(A,e);var t={}.toString.call(A).slice(8,-1);return"Object"===t&&A.constructor&&(t=A.constructor.name),"Map"===t||"Set"===t?Array.from(A):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?i(A,e):void 0}}function i(A,e){(null==e||e>A.length)&&(e=A.length);for(var t=0,n=Array(e);t<e;t++)n[t]=A[t];return n}t.r(e),t.d(e,{disconnectObservers:function(){return u},getVitalsVal:function(){return l},observeVitals:function(){return c}});var s={lcp:null,cls:null,fid:null},a={lcp:[],cls:[],fid:[]};function c(){try{s.lcp=new PerformanceObserver((function(A){var e,t=n(A.getEntries());try{for(t.s();!(e=t.n()).done;){var r=e.value;a.lcp.push(r.startTime)}}catch(A){t.e(A)}finally{t.f()}})),s.lcp.observe({type:"largest-contentful-paint",buffered:!0}),A=0,e=0,t=[],s.cls=new PerformanceObserver((function(r){var o,i=n(r.getEntries());try{for(i.s();!(o=i.n()).done;){var s=o.value;if(!s.hadRecentInput){var c=t[0],u=t[t.length-1];e&&s.startTime-u.startTime<1e3&&s.startTime-c.startTime<5e3?(e+=s.value,t.push(s)):(e=s.value,t=[s]),e>A&&(A=e,a.cls.push(A))}}}catch(A){i.e(A)}finally{i.f()}})),s.cls.observe({type:"layout-shift",buffered:!0}),s.fid=new PerformanceObserver((function(A){var e,t=n(A.getEntries());try{for(t.s();!(e=t.n()).done;){var r=e.value,o=r.processingStart-r.startTime;a.fid.push(o)}}catch(A){t.e(A)}finally{t.f()}})),s.fid.observe({type:"first-input",buffered:!0})}catch(A){}var A,e,t}function u(){try{s.lcp.disconnect(),s.fid.disconnect(),s.cls.disconnect()}catch(A){}}function l(){var A=Math.round(a.lcp.pop()),e=a.fid.pop(),t=Math.max.apply(Math,r(a.cls));return{lcp:A>=0?A:-1,fid:e>=0?parseFloat(e.toFixed(2)):-1,cls:t>=0?parseFloat(t.toFixed(3)):-1}}},"./src/whiteScreen.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return s}});var n=t("./src/log.js"),r=t("./src/util.js"),o=t("./src/scripts/html2canvas.min.js"),i=t.n(o);function s(){var A=r.UTIL.getConfig(),e=A.setting,t=void 0===e?"":e,o=A.options,s=void 0===o?{}:o;if(!(t.includes("forbidWhiteScreen")||r.UTIL.getDeviceIsMobile()||r.UTIL.getIsInIframe())){r.UTIL.addLoadEvent((function(){if(window.requestIdleCallback)return window.requestIdleCallback(l);return setTimeout(l,1)}));var a=s.whiteScreen,c=(void 0===a?{}:a).rootElements||["html","body","#app","#root"]}function u(A){if(!A)return!1;var e=function(A){return A.id?"#"+A.id:A.className&&"string"==typeof A.className?"."+A.className.split(" ").filter((function(A){return Boolean(A)})).join("."):A.nodeName.toLowerCase()}(A);return-1!==c.indexOf(e)}function l(){for(var A,e,t=0,r=(null===(A=document.documentElement)||void 0===A?void 0:A.scrollHeight)||document.body.scrollHeight,o=(null===(e=document.documentElement)||void 0===e?void 0:e.scrollWidth)||document.body.scrollWidth,s=1;s<=9;s++){var a,c,l,d,B=null===(a=document)||void 0===a?void 0:a.elementsFromPoint(o*s/10,r/2),g=null===(c=document)||void 0===c?void 0:c.elementsFromPoint(o/2,r*s/10),f=null===(l=document)||void 0===l?void 0:l.elementsFromPoint(o*s/10,r*s/10),p=null===(d=document)||void 0===d?void 0:d.elementsFromPoint(o*s/10,r-r*s/10);u(B[0])&&t++,5!==s&&(u(g[0])&&t++,u(f[0])&&t++,u(p[0])&&t++)}33===t&&(document.createElement("canvas").getContext?i()(document.body,{useWebWorker:!0,scale:.75,useCORS:!0,ignoreElements:function(A){var e;return["script","svg","img","link","audio","video","object","embed"].includes(null===(e=A.nodeName)||void 0===e?void 0:e.toLowerCase())}}).then((function(A){try{var e=A.toDataURL("image/png");n.LOG.report("ws","ws",{img:e||"-1"})}catch(A){n.LOG.report("ws","ws",{img:"-1"})}})).catch((function(){n.LOG.report("ws","ws",{img:"-1"})})):n.LOG.report("ws","ws",{img:"-1"}))}}},"./src/workerScript.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return n}});const n="\nconst STATUS_MAP={\n    normal:1,// 恢复状态\n    crash:0,// 崩溃状态\n}\nlet queryStr = ''; // 请求字符串参数\nlet domain = '';\nlet unResponseCount = 0; // 主线程没有响应的次数\nlet jsHeapList = []; // 崩溃前10s内存的变化列表\nconst healthMap = new Map(); // 记录心跳的map\nlet lastCheckTime = 0; // 上次发起心跳检测的时间\nlet startCrashTime = 0; // 崩溃开始时间\nlet crashId = ''; // 崩溃id,代表一次崩溃的唯一标识\nlet lastClickInfo = null;\nself.addEventListener('message', (e) => {\n    lastClickInfo = e.data.lastClickInfo || lastClickInfo;\n    queryStr = e.data.queryStr || queryStr;\n    domain = e.data.domain || domain;\n    crashId = e.data.crashId || crashId;\n    // 只记录崩溃前最多10s的js内存变化\n    if (jsHeapList.length >= 10) {\n        jsHeapList.shift();\n    }\n    if (e.data.jsHeap) {\n        jsHeapList.push(e.data.jsHeap);\n    }\n    // 接收到主线程响应,将状态注册为alive\n    healthMap.set(e.data.checkTime, 'alive');\n    if (e.data.type === 'register') {\n        lastCheckTime = e.data.checkTime;\n    }\n});\n// 不直接使用setInterval,防止setInterval长时间运行导致内存泄漏\nfunction timerFun(cb, time) {\n    let timer=null;\n    return function backFun() {\n        clearTimeout(timer);\n        timer=setTimeout(() => {\n            cb();\n            backFun();\n        },time)\n    }\n}\ntimerFun(getMainStatus, 1000)()\n\nfunction getMainStatus() {\n    const timeInterval = (new Date().getTime() - lastCheckTime) / 1000;\n    // 防止用户睡眠电脑,导致崩溃时间超长\n    if(startCrashTime && timeInterval > 30) {\n        // 休眠时间超过30s将状态全部重置\n        unResponseCount = 0;\n        jsHeapList = [];\n        healthMap = new Map();\n        lastCheckTime = new Date().getTime();\n        startCrashTime = 0;\n        crashId = '';\n        lastClickInfo = null;\n    }\n    // 上次发出的消息还没有回复,开始计数\n    if (healthMap.get(lastCheckTime) !== 'alive') {\n        unResponseCount++;\n        // 崩溃开始时间\n        startCrashTime = startCrashTime || lastCheckTime;\n        if (unResponseCount >= 5 && unResponseCount <= 30 && crashId ) {\n            // 满足条件,上报崩溃\n            reportCrash(STATUS_MAP.crash, crashId);\n        }\n    } else {\n        if (startCrashTime && crashId && unResponseCount > 5) {\n            // 崩溃恢复\n            reportCrash(STATUS_MAP.normal, crashId);\n            crashId = '';\n            lastClickInfo = null;\n        }\n        unResponseCount = 0;\n        startCrashTime = 0;\n        // 删除健康的心跳,防止map结构越来越大\n        healthMap.delete(lastCheckTime);\n    }\n    const checkTime = new Date().getTime();\n    self.postMessage({ checkTime,crashId });\n    lastCheckTime = checkTime;\n}\nfunction reportCrash(status, cid) {\n    const postUrl = domain + '/fr?' + queryStr;\n    const info={\n        js_heap_list: jsHeapList, // 崩溃前10s js内存变化\n        last_click_info: lastClickInfo, //最后的点击行为\n    }\n    request(postUrl, 'post', [\n        {\n            t: 'crash',\n            val: cid,\n            ts: new Date().getTime(),\n            fs:startCrashTime, // 崩溃开始时间\n            dr: new Date().getTime() - startCrashTime, // 崩溃时长\n            sc:status, // 是否恢复\n            info:JSON.stringify(info)\n        },\n    ]).catch((error) => {});\n}\nfunction request(_url, _method, _params) {\n    return new Promise((resolve, reject) => {\n        const xhr = new XMLHttpRequest();\n        xhr.timeout = 3000;\n        xhr.open(_method, _url);\n        xhr.send(JSON.stringify(_params));\n        xhr.onreadystatechange = () => {\n            if (xhr.readyState === 4) {\n                try {\n                    const res = JSON.parse(xhr.responseText);\n                    if (res.result) {\n                        resolve(res);\n                    }\n                } catch (e) {\n                    reject(e);\n                }\n            }\n        };\n    });\n}\n"},"./src/xhr.js":function(A,e,t){"use strict";t.r(e),t.d(e,{default:function(){return a}});var n=t("./src/apiUtil.js"),r=t("./src/log.js"),o=t("./src/util.js");function i(){var A={},e=!1,t=new window.orig_XMLHttpRequest,i=t.open,a=t.send,c="";return t.open=function(){for(var a=arguments.length,u=new Array(a),l=0;l<a;l++)u[l]=arguments[l];var d=u[0],B=u[1];c=B,e||(e=!0,function(A,e,t,i){A.addEventListener(i,(function(){setTimeout((function(){if(o.UTIL.apiCanReport(t)){!function(A,e){var t=o.UTIL.getFullApiPath(e),r=(0,n.getApiPerformance)(t),i=r.fs,s=r.dr,a=r.stt,c=r.protocol,u=r.transfer_size;A.fs=i||A.fs,A.dr=s||Math.round(performance.now()-A.fs),A.stt=a,A.protocol=c,A.transfer_size=u}(e,t),function(A,e){A.sc=e.status}(e,A),function(A,e){var t;A.msg=e.statusText;try{o.UTIL.responseHasErr(e.status,JSON.parse(e.responseText)||{})&&(r.LOG.FURION.log("submitCache"),A.msg=e.responseText)}catch(t){"text"===e.responseType&&(A.msg=e.responseText)}var n=o.UTIL.getConfig().customApiMsg;n&&(A.msg=n(e.status,e.responseText,e.responseURL));A.msg=null===(t=A.msg)||void 0===t?void 0:t.substr(0,o.UTIL.limitLen)}(e,A),function(A,e){var t=s(e);A.tid=(0,n.getTraceId)(t)}(e,A),function(A,e){e.getAllResponseHeaders().indexOf("Content-Length")>=0&&(A.cl=parseInt(e.getResponseHeader("Content-Length")))}(e,A),function(A,e){var t=s(e);A.headers=o.UTIL.getReportHeaders(t)}(e,A);var i=o.UTIL.getApiPathWithQuery(t);r.LOG.report("api",i,e)}}),100)}),!1)}(t,A,B,"load")),A.mtd=d;try{return i.apply(t,u)}catch(A){throw A}},t.send=function(){A.fs=Math.round(performance.now());for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=o.UTIL.getReportParams(c,n[0]);if(i&&(A.info=i),void 0===A.code||4!==A.code)return a.apply(t,n)},t.resource=A,t}function s(A){var e=new Map,t=A.getAllResponseHeaders();t&&t.trim().split(/[\r\n]+/).forEach((function(A){var t=A.split(": "),n=t.shift(),r=t.join(": ");e.set(n,r)}));return e}const a=function(){var A,e,t,n,r,o;window.proxyXMLHttpRequest&&window.proxyXMLHttpRequest===window.XMLHttpRequest||(window.proxyXMLHttpRequest&&window.orig_XMLHttpRequest&&window.orig_XMLHttpRequest===window.XMLHttpRequest?window.XMLHttpRequest=window.proxyXMLHttpRequest:(window.orig_XMLHttpRequest=window.orig_XMLHttpRequest||window.XMLHttpRequest,i.prototype=window.XMLHttpRequest.prototype,window.XMLHttpRequest=i,A=window.orig_XMLHttpRequest,e=A.UNSENT,t=A.OPENED,n=A.HEADERS_RECEIVED,r=A.LOADING,o=A.DONE,Object.assign(window.XMLHttpRequest,{UNSENT:e,OPENED:t,HEADERS_RECEIVED:n,LOADING:r,DONE:o})))}}},e={};function t(n){var r=e[n];if(void 0!==r)return r.exports;var o=e[n]={exports:{}};return A[n].call(o.exports,o,o.exports,t),o.exports}t.n=function(A){var e=A&&A.__esModule?function(){return A.default}:function(){return A};return t.d(e,{a:e}),e},t.d=function(A,e){for(var n in e)t.o(e,n)&&!t.o(A,n)&&Object.defineProperty(A,n,{enumerable:!0,get:e[n]})},t.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(A){if("object"==typeof window)return window}}(),t.o=function(A,e){return Object.prototype.hasOwnProperty.call(A,e)},t.r=function(A){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(A,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(A,"__esModule",{value:!0})};var n={};!function(){"use strict";t.r(n);t("./node_modules/core-js/es/string/includes.js"),t("./node_modules/core-js/es/array/find.js"),t("./node_modules/core-js/es/array/includes.js"),t("./node_modules/core-js/es/array/filter.js"),t("./node_modules/core-js/modules/es.object.assign.js"),t("./src/init.js");var A=t("./node_modules/uuid/dist/esm-browser/v4.js"),e=t("./src/uba.js"),r=t("./src/js-track.js"),o=t("./src/xhr.js"),i=t("./src/fetch.js"),s=t("./src/perf.js"),a=t("./src/longtask.js"),c=t("./src/util.js"),u=t("./src/check-upload.js"),l=t("./src/log.js"),d=t("./src/finger-print.js"),B=t("./src/config.js"),g=t("./src/rtti.js"),f=t("./src/codeLess.js"),p=t("./src/fps.js"),h=t("./src/crash.js"),w=t("./src/sendBeaconReport.js"),C=t("./src/whiteScreen.js"),Q=t("./src/resources.js"),m=t("./src/ABTest.js");!window.FURION&&"undefined"!=typeof Promise&&c.UTIL.isValidAnalysisCookie&&(function(){window.FURION=l.LOG.FURION,c.UTIL.checkHashMode();var e=c.UTIL.getConfig(),t=e.domain,n=e.setting;B.CONFIG.GLOBAL_VARS.domain=t,B.CONFIG.GLOBAL_VARS.setting=n,B.CONFIG.GLOBAL_VARS.randomId=(0,d.setRandomId)(),B.CONFIG.GLOBAL_VARS.eno=c.UTIL.getCookie("login_uid"),B.CONFIG.GLOBAL_VARS.lastUrl=c.UTIL.getReportUrl(),B.CONFIG.VISIT_INFO.os=c.UTIL.getOperationSys(),B.CONFIG.VISIT_INFO.pid=(0,A.default)();var r=(0,A.default)().split("-").join("");B.CONFIG.VISIT_INFO.pvid=r,B.CONFIG.GLOBAL_VARS.lastPvid=r,B.CONFIG.VISIT_INFO.browser=c.UTIL.getBrowserInfo(),window.FURION.fetchABTest=m.fetchABTest,c.UTIL.addLoadEvent((function(){"cn"===window.location.hostname.split(".").pop()&&c.UTIL.hasPermissions()&&(B.CONFIG.GLOBAL_VARS.domain="https://furiondata.myhuaweicloud.cn/furiondataserver"),c.UTIL.checkSdkWcm()&&(B.CONFIG.GLOBAL_VARS.domain="https://furiondata.cloudbu.huawei.com/api/furiondataserver")}))}(),function(){var A=B.CONFIG.GLOBAL_VARS.setting;if(performance.now){c.UTIL.setFrVar({setConfig:c.UTIL.setConfig,log:l.LOG.FURION.log,log2:l.LOG.FURION.log2,version:B.CONFIG.GLOBAL_VARS.VERSION}),(0,u.checkAfterLoad)(),c.UTIL.clearBufferOnfull();var t={perf:s.default,uba:function(){f.Codeless.init(),(0,e.default)()},jsTrack:r.default,xhr:o.default,api:function(){(0,o.default)(),i.FETCH.proxyFetch()},fetch:i.FETCH.proxyFetch,longtask:a.default,rtti:g.default,crash:h.CRASH.crash},n=["crash"];Array.from(new Set(A.split(","))).forEach((function(A){t[A]&&(n.includes(A)?c.UTIL.addLoadEvent(t[A]):t[A]())})),(0,w.default)(),(0,Q.default)(),(0,C.default)(),c.UTIL.addLoadEvent((function(){c.UTIL.setIAMInfoFromCFUI(),(0,p.default)()})),c.UTIL.handlePageVisibilityChange()}else c.UTIL.setFrVar({runSDKErr:!0})}()),c.UTIL.isIntlStation()&&document.addEventListener("policyCookiesEvent",(function(A){A.detail&&0===A.detail.analysis&&(c.UTIL.setCookie(B.CONFIG.COOKIES.ssid,"",-1),B.CONFIG.GLOBAL_VARS.lastSsid="",c.UTIL.setCookie(B.CONFIG.COOKIES.frid,"",-1),c.UTIL.setCookie(B.CONFIG.COOKIES.w3Fid,"",-1))}))}()}();